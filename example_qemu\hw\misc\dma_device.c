/*
 * QEMU DMA Memory Access Device
 * 
 * This device provides network-based DMA access to guest physical memory
 * for process memory manipulation from external systems.
 * 
 * Author: Generated for TestDMA project
 * License: GPL-2.0+
 */

#include "qemu/osdep.h"
#include "qemu/cutils.h"
#include "hw/pci/pci.h"
#include "hw/qdev-properties.h"
#include "qapi/error.h"
#include "qemu/log.h"
#include "qemu/module.h"
#include "qemu/sockets.h"
#include "qemu/thread.h"
#include "hw/irq.h"
#include "migration/vmstate.h"
#include "exec/cpu-all.h"
#include "target/i386/cpu.h"
#include "cpu_physical_memory.h"

#define TYPE_DMA_DEVICE "dma-device"
#define DMA_DEVICE(obj) OBJECT_CHECK(DMADeviceState, (obj), TYPE_DMA_DEVICE)

#define DMA_DEVICE_VENDOR_ID    0x1337
#define DMA_DEVICE_DEVICE_ID    0x0001
#define DMA_DEVICE_REVISION     0x01
#define DMA_DEVICE_CLASS_ID     0x050000

/* Protocol constants */
#define DMA_MAGIC               0x444D4100  /* "DMA\0" */
#define DMA_VERSION             1

/* Command types */
#define DMA_CMD_READ_MEMORY     0x01
#define DMA_CMD_WRITE_MEMORY    0x02
#define DMA_CMD_GET_PROCESS     0x03
#define DMA_CMD_GET_MODULE      0x04
#define DMA_CMD_TRANSLATE_VA    0x05
#define DMA_CMD_PING            0x06

/* Response types */
#define DMA_RESP_SUCCESS        0x00
#define DMA_RESP_ERROR          0x01
#define DMA_RESP_INVALID_CMD    0x02
#define DMA_RESP_INVALID_ADDR   0x03

/* Maximum packet size */
#define DMA_MAX_PACKET_SIZE     4096
#define DMA_MAX_READ_SIZE       2048

/* Network settings */
#define DMA_DEFAULT_PORT        13337

typedef struct {
    uint32_t magic;
    uint32_t version;
    uint32_t command;
    uint32_t length;
    uint64_t address;
    uint32_t size;
    uint32_t pid;
    uint32_t reserved[4];
} __attribute__((packed)) DMAPacketHeader;

typedef struct {
    uint32_t magic;
    uint32_t response;
    uint32_t length;
    uint32_t reserved;
    uint8_t data[];
} __attribute__((packed)) DMAPacketResponse;

typedef struct {
    PCIDevice parent_obj;
    
    /* Network thread */
    QemuThread network_thread;
    bool thread_running;
    int server_socket;
    uint16_t port;
    
    /* Memory access mutex */
    QemuMutex memory_mutex;
    
    /* Statistics */
    uint64_t packets_received;
    uint64_t packets_sent;
    uint64_t bytes_read;
    uint64_t bytes_written;
} DMADeviceState;

/* Forward declarations */
static void dma_device_network_thread(void *opaque);
static int dma_handle_client(DMADeviceState *s, int client_socket);
static int dma_process_packet(DMADeviceState *s, int client_socket, 
                              DMAPacketHeader *header, uint8_t *data);

/* Windows-specific structures for process enumeration */
typedef struct {
    uint64_t eprocess;
    uint32_t pid;
    char name[16];
    uint64_t cr3;
    uint64_t peb;
} ProcessInfo;

typedef struct {
    uint64_t base_address;
    uint32_t size;
    char name[256];
    uint64_t entry_point;
} ModuleInfo;

/* Helper functions for Windows kernel structures */
static uint64_t find_kernel_base(DMADeviceState *s)
{
    /* Scan for NT kernel base in typical Windows memory layout */
    uint64_t kernel_base = 0xFFFFF80000000000ULL; /* Windows x64 kernel space */
    uint8_t signature[2] = {0x4D, 0x5A}; /* MZ header */
    uint8_t buffer[2];
    CPUState *cpu = qemu_get_cpu(0);
    
    if (!cpu) {
        return 0;
    }
    
    for (uint64_t addr = kernel_base; addr < 0xFFFFF90000000000ULL; addr += 0x1000) {
        if (cpu_physical_memory_rw(addr, buffer, 2, 0) == 0) {
            if (buffer[0] == signature[0] && buffer[1] == signature[1]) {
                return addr;
            }
        }
    }
    return 0;
}

static uint64_t find_pshead_offset(DMADeviceState *s, uint64_t kernel_base)
{
    /* This would need to be adapted based on Windows version */
    /* For Windows 10/11 x64, PsActiveProcessHead is typically at offset from kernel base */
    return kernel_base + 0x123456; /* Placeholder - needs actual offset */
}

static int enumerate_processes(DMADeviceState *s, ProcessInfo *processes, int max_count)
{
    uint64_t kernel_base = find_kernel_base(s);
    if (!kernel_base) {
        return -1;
    }
    
    uint64_t pshead = find_pshead_offset(s, kernel_base);
    if (!pshead) {
        return -1;
    }
    
    int count = 0;
    uint64_t current_entry = pshead;
    CPUState *cpu = qemu_get_cpu(0);
    
    if (!cpu) {
        return -1;
    }
    
    /* Walk the process list */
    for (int i = 0; i < max_count && i < 1000; i++) {
        uint64_t eprocess;
        if (cpu_physical_memory_rw(current_entry, (uint8_t*)&eprocess, 8, 0) != 0) {
            break;
        }
        
        if (eprocess == pshead) {
            break; /* Back to head */
        }
        
        ProcessInfo *proc = &processes[count];
        proc->eprocess = eprocess;
        
        /* Read PID (offset varies by Windows version) */
        uint64_t pid_offset = eprocess + 0x2E8; /* Windows 10 x64 offset */
        cpu_physical_memory_rw(pid_offset, (uint8_t*)&proc->pid, 4, 0);
        
        /* Read process name */
        uint64_t name_offset = eprocess + 0x5A8; /* ImageFileName offset */
        cpu_physical_memory_rw(name_offset, (uint8_t*)proc->name, 15, 0);
        proc->name[15] = 0;
        
        /* Read CR3 (page directory base) */
        uint64_t cr3_offset = eprocess + 0x28; /* DirectoryTableBase offset */
        cpu_physical_memory_rw(cr3_offset, (uint8_t*)&proc->cr3, 8, 0);
        
        /* Read PEB */
        uint64_t peb_offset = eprocess + 0x550; /* Peb offset */
        cpu_physical_memory_rw(peb_offset, (uint8_t*)&proc->peb, 8, 0);
        
        count++;
        current_entry = eprocess; /* Next EPROCESS in list */
    }
    
    return count;
}

static uint64_t virtual_to_physical(DMADeviceState *s, uint64_t virtual_addr, uint64_t cr3)
{
    /* Implement page table walking for virtual to physical translation */
    uint64_t pml4_index = (virtual_addr >> 39) & 0x1FF;
    uint64_t pdpt_index = (virtual_addr >> 30) & 0x1FF;
    uint64_t pd_index = (virtual_addr >> 21) & 0x1FF;
    uint64_t pt_index = (virtual_addr >> 12) & 0x1FF;
    uint64_t page_offset = virtual_addr & 0xFFF;
    CPUState *cpu = qemu_get_cpu(0);
    
    if (!cpu) {
        return 0;
    }
    
    /* PML4 */
    uint64_t pml4_entry_addr = (cr3 & 0xFFFFFFFFFFFFF000ULL) + (pml4_index * 8);
    uint64_t pml4_entry;
    if (cpu_physical_memory_rw(pml4_entry_addr, (uint8_t*)&pml4_entry, 8, 0) != 0) {
        return 0;
    }
    if (!(pml4_entry & 1)) return 0; /* Not present */
    
    /* PDPT */
    uint64_t pdpt_entry_addr = (pml4_entry & 0xFFFFFFFFFFFFF000ULL) + (pdpt_index * 8);
    uint64_t pdpt_entry;
    if (cpu_physical_memory_rw(pdpt_entry_addr, (uint8_t*)&pdpt_entry, 8, 0) != 0) {
        return 0;
    }
    if (!(pdpt_entry & 1)) return 0; /* Not present */
    
    /* Check for 1GB page */
    if (pdpt_entry & 0x80) {
        return (pdpt_entry & 0xFFFFFFFFC0000000ULL) + (virtual_addr & 0x3FFFFFFFULL);
    }
    
    /* PD */
    uint64_t pd_entry_addr = (pdpt_entry & 0xFFFFFFFFFFFFF000ULL) + (pd_index * 8);
    uint64_t pd_entry;
    if (cpu_physical_memory_rw(pd_entry_addr, (uint8_t*)&pd_entry, 8, 0) != 0) {
        return 0;
    }
    if (!(pd_entry & 1)) return 0; /* Not present */
    
    /* Check for 2MB page */
    if (pd_entry & 0x80) {
        return (pd_entry & 0xFFFFFFFFFFFE0000ULL) + (virtual_addr & 0x1FFFFFULL);
    }
    
    /* PT */
    uint64_t pt_entry_addr = (pd_entry & 0xFFFFFFFFFFFFF000ULL) + (pt_index * 8);
    uint64_t pt_entry;
    if (cpu_physical_memory_rw(pt_entry_addr, (uint8_t*)&pt_entry, 8, 0) != 0) {
        return 0;
    }
    if (!(pt_entry & 1)) return 0; /* Not present */
    
    return (pt_entry & 0xFFFFFFFFFFFFF000ULL) + page_offset;
}

static int dma_process_packet(DMADeviceState *s, int client_socket, 
                              DMAPacketHeader *header, uint8_t *data)
{
    DMAPacketResponse response;
    uint8_t *response_data = NULL;
    int response_data_size = 0;
    int ret = 0;
    
    memset(&response, 0, sizeof(response));
    response.magic = DMA_MAGIC;
    response.response = DMA_RESP_SUCCESS;
    
    qemu_mutex_lock(&s->memory_mutex);
    
    switch (header->command) {
        case DMA_CMD_PING:
            /* Simple ping/pong for connection testing */
            response.length = 4;
            response_data = g_malloc(4);
            *(uint32_t*)response_data = 0x504F4E47; /* "PONG" */
            response_data_size = 4;
            break;        case DMA_CMD_READ_MEMORY:
            if (header->size > DMA_MAX_READ_SIZE) {
                response.response = DMA_RESP_ERROR;
                break;
            }
            
            response_data = g_malloc(header->size);
            if (cpu_physical_memory_rw(header->address, response_data, header->size, 0) == 0) {
                response.length = header->size;
                response_data_size = header->size;
                s->bytes_read += header->size;
            } else {
                response.response = DMA_RESP_INVALID_ADDR;
                g_free(response_data);
                response_data = NULL;
            }
            break;        case DMA_CMD_WRITE_MEMORY:
            if (header->length > DMA_MAX_READ_SIZE) {
                response.response = DMA_RESP_ERROR;
                break;
            }
            
            if (cpu_physical_memory_rw(header->address, data, header->length, 1) == 0) {
                s->bytes_written += header->length;
            } else {
                response.response = DMA_RESP_INVALID_ADDR;
            }
            break;
            
        case DMA_CMD_GET_PROCESS:
            {
                ProcessInfo processes[100];
                int count = enumerate_processes(s, processes, 100);
                
                if (count > 0) {
                    response_data_size = count * sizeof(ProcessInfo);
                    response_data = g_malloc(response_data_size);
                    memcpy(response_data, processes, response_data_size);
                    response.length = response_data_size;
                } else {
                    response.response = DMA_RESP_ERROR;
                }
            }
            break;
            
        case DMA_CMD_TRANSLATE_VA:
            {
                uint64_t physical = virtual_to_physical(s, header->address, header->pid);
                response_data_size = 8;
                response_data = g_malloc(8);
                *(uint64_t*)response_data = physical;
                response.length = 8;
            }
            break;
            
        default:
            response.response = DMA_RESP_INVALID_CMD;
            break;
    }
    
    qemu_mutex_unlock(&s->memory_mutex);
    
    /* Send response header */
    if (send(client_socket, (char*)&response, sizeof(response), 0) != sizeof(response)) {
        ret = -1;
        goto cleanup;
    }
    
    /* Send response data if any */
    if (response_data && response_data_size > 0) {
        if (send(client_socket, (char*)response_data, response_data_size, 0) != response_data_size) {
            ret = -1;
            goto cleanup;
        }
    }
    
    s->packets_sent++;

cleanup:
    if (response_data) {
        g_free(response_data);
    }
    return ret;
}

static int dma_handle_client(DMADeviceState *s, int client_socket)
{
    DMAPacketHeader header;
    uint8_t *data = NULL;
    int ret = 0;
    
    while (s->thread_running) {
        /* Receive header */
        int bytes_received = recv(client_socket, (char*)&header, sizeof(header), 0);
        if (bytes_received <= 0) {
            break;
        }
        
        if (bytes_received != sizeof(header)) {
            continue;
        }
        
        /* Validate header */
        if (header.magic != DMA_MAGIC || header.version != DMA_VERSION) {
            continue;
        }
        
        /* Receive data if any */
        if (header.length > 0) {
            if (header.length > DMA_MAX_PACKET_SIZE) {
                break; /* Invalid packet size */
            }
            
            data = g_malloc(header.length);
            bytes_received = recv(client_socket, (char*)data, header.length, MSG_WAITALL);
            if (bytes_received != header.length) {
                g_free(data);
                break;
            }
        }
        
        /* Process packet */
        ret = dma_process_packet(s, client_socket, &header, data);
        
        if (data) {
            g_free(data);
            data = NULL;
        }
        
        s->packets_received++;
        
        if (ret != 0) {
            break;
        }
    }
    
    if (data) {
        g_free(data);
    }
    
    return ret;
}

static void dma_device_network_thread(void *opaque)
{
    DMADeviceState *s = DMA_DEVICE(opaque);
    struct sockaddr_in server_addr, client_addr;
    socklen_t client_len = sizeof(client_addr);
    int client_socket;
    
    /* Create server socket */
    s->server_socket = socket(AF_INET, SOCK_STREAM, 0);
    if (s->server_socket < 0) {
        qemu_log("DMA Device: Failed to create socket\n");
        return;
    }
    
    /* Set socket options */
    int opt = 1;
    setsockopt(s->server_socket, SOL_SOCKET, SO_REUSEADDR, (char*)&opt, sizeof(opt));
    
    /* Bind socket */
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_addr.s_addr = INADDR_ANY;
    server_addr.sin_port = htons(s->port);
    
    if (bind(s->server_socket, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
        qemu_log("DMA Device: Failed to bind socket to port %d\n", s->port);
        closesocket(s->server_socket);
        return;
    }
    
    /* Listen for connections */
    if (listen(s->server_socket, 5) < 0) {
        qemu_log("DMA Device: Failed to listen on socket\n");
        closesocket(s->server_socket);
        return;
    }
    
    qemu_log("DMA Device: Listening on port %d\n", s->port);
    
    /* Accept connections */
    while (s->thread_running) {
        client_socket = accept(s->server_socket, (struct sockaddr*)&client_addr, &client_len);
        if (client_socket < 0) {
            if (s->thread_running) {
                qemu_log("DMA Device: Failed to accept connection\n");
            }
            continue;
        }
        
        qemu_log("DMA Device: Client connected from %s\n", inet_ntoa(client_addr.sin_addr));
        
        /* Handle client (blocking) */
        dma_handle_client(s, client_socket);
        
        closesocket(client_socket);
        qemu_log("DMA Device: Client disconnected\n");
    }
    
    closesocket(s->server_socket);
}

static void dma_device_realize(PCIDevice *pci_dev, Error **errp)
{
    DMADeviceState *s = DMA_DEVICE(pci_dev);
    
    /* Initialize mutex */
    qemu_mutex_init(&s->memory_mutex);
    
    /* Set default port if not specified */
    if (s->port == 0) {
        s->port = DMA_DEFAULT_PORT;
    }
    
    /* Start network thread */
    s->thread_running = true;
    qemu_thread_create(&s->network_thread, "dma-network", 
                       dma_device_network_thread, s, QEMU_THREAD_DETACHED);
    
    qemu_log("DMA Device: Initialized on port %d\n", s->port);
}

static void dma_device_unrealize(PCIDevice *pci_dev)
{
    DMADeviceState *s = DMA_DEVICE(pci_dev);
    
    /* Stop network thread */
    s->thread_running = false;
    if (s->server_socket >= 0) {
        closesocket(s->server_socket);
    }
    
    /* Cleanup mutex */
    qemu_mutex_destroy(&s->memory_mutex);
    
    qemu_log("DMA Device: Uninitialized\n");
}

static Property dma_device_properties[] = {
    DEFINE_PROP_UINT16("port", DMADeviceState, port, DMA_DEFAULT_PORT),
    DEFINE_PROP_END_OF_LIST(),
};

static void dma_device_class_init(ObjectClass *klass, void *data)
{
    DeviceClass *dc = DEVICE_CLASS(klass);
    PCIDeviceClass *k = PCI_DEVICE_CLASS(klass);
    
    k->realize = dma_device_realize;
    k->exit = dma_device_unrealize;
    k->vendor_id = DMA_DEVICE_VENDOR_ID;
    k->device_id = DMA_DEVICE_DEVICE_ID;
    k->revision = DMA_DEVICE_REVISION;
    k->class_id = DMA_DEVICE_CLASS_ID;
    
    dc->desc = "DMA Memory Access Device";
    device_class_set_props(dc, dma_device_properties);
    set_bit(DEVICE_CATEGORY_MISC, dc->categories);
}

static const TypeInfo dma_device_info = {
    .name = TYPE_DMA_DEVICE,
    .parent = TYPE_PCI_DEVICE,
    .instance_size = sizeof(DMADeviceState),
    .class_init = dma_device_class_init,
    .interfaces = (InterfaceInfo[]) {
        { INTERFACE_CONVENTIONAL_PCI_DEVICE },
        { },
    },
};

static void dma_device_register_types(void)
{
    type_register_static(&dma_device_info);
}

type_init(dma_device_register_types)

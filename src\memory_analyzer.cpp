/*
 * Memory Analyzer Implementation
 * High-level memory analysis and process enumeration
 */

#include "memory_analyzer.h"
#include <iostream>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <cctype>

MemoryAnalyzer::MemoryAnalyzer()
    : m_dma_client(nullptr), m_volatility(nullptr), m_initialized(false), 
      m_memory_analyzed(false), m_qemu_port(DMA_DEFAULT_PORT) {
}

MemoryAnalyzer::~MemoryAnalyzer() {
    Shutdown();
}

bool MemoryAnalyzer::Initialize(const std::string& qemu_host, uint16_t qemu_port) {
    if (m_initialized) {
        return true;
    }

    m_qemu_host = qemu_host;
    m_qemu_port = qemu_port;

    // Initialize DMA client
    m_dma_client = std::make_unique<DMAClient>(qemu_host, qemu_port);
    if (!m_dma_client->Connect()) {
        SetError("Failed to connect to QEMU DMA device: " + m_dma_client->GetLastError());
        return false;
    }

    // Test connection
    if (!m_dma_client->Ping()) {
        SetError("DMA device ping failed");
        return false;
    }

    // Get memory layout
    m_memory_regions = m_dma_client->GetMemoryLayout();

    m_initialized = true;
    m_last_error.clear();
    return true;
}

bool MemoryAnalyzer::SetVolatilityPath(const std::string& volatility_path) {
    m_volatility_path = volatility_path;
    
    // Initialize Volatility interface
    m_volatility = std::make_unique<VolatilityInterface>();
    if (!m_volatility->Initialize(volatility_path)) {
        SetError("Failed to initialize Volatility: " + m_volatility->GetLastError());
        return false;
    }

    return true;
}

void MemoryAnalyzer::Shutdown() {
    if (m_dma_client) {
        m_dma_client->Disconnect();
        m_dma_client.reset();
    }
    
    m_volatility.reset();
    m_initialized = false;
    m_memory_analyzed = false;
}

bool MemoryAnalyzer::DumpMemoryToFile(const std::string& output_file, uint64_t size) {
    if (!m_initialized || !m_dma_client) {
        SetError("Not initialized");
        return false;
    }

    if (!m_dma_client->DumpMemory(0x0, size, output_file)) {
        SetError("Memory dump failed: " + m_dma_client->GetLastError());
        return false;
    }

    m_current_memory_file = output_file;
    return true;
}

bool MemoryAnalyzer::AnalyzeMemoryDump(const std::string& memory_file) {
    if (!m_volatility) {
        // Try to auto-detect Volatility if not set
        if (m_volatility_path.empty()) {
            m_volatility_path = "volatility3/vol.py";
        }
        
        m_volatility = std::make_unique<VolatilityInterface>();
        if (!m_volatility->Initialize(m_volatility_path)) {
            SetError("Volatility not available. Please set volatility path.");
            return false;
        }
    }

    // Validate memory file
    if (!m_volatility->ValidateMemoryFile(memory_file)) {
        SetError("Invalid memory file: " + memory_file);
        return false;
    }

    m_current_memory_file = memory_file;

    // Get system information
    m_system_info = m_volatility->GetSystemInfo(memory_file);

    // Parse process list
    std::string pslist_output = m_volatility->ListProcesses(memory_file);
    if (!ParseProcessList(pslist_output)) {
        SetError("Failed to parse process list");
        return false;
    }

    m_memory_analyzed = true;
    return true;
}

bool MemoryAnalyzer::ParseProcessList(const std::string& pslist_output) {
    m_processes.clear();
    
    std::istringstream iss(pslist_output);
    std::string line;
    bool header_found = false;

    while (std::getline(iss, line)) {
        // Skip until we find the header
        if (!header_found) {
            if (line.find("PID") != std::string::npos && 
                line.find("PPID") != std::string::npos && 
                line.find("ImageFileName") != std::string::npos) {
                header_found = true;
            }
            continue;
        }

        // Skip empty lines
        if (line.empty() || line.find_first_not_of(" \t") == std::string::npos) {
            continue;
        }

        // Parse process line
        std::istringstream line_stream(line);
        std::vector<std::string> tokens;
        std::string token;
        
        while (line_stream >> token) {
            tokens.push_back(token);
        }

        if (tokens.size() >= 8) {
            try {
                ProcessEntry process;
                process.pid = std::stoul(tokens[0]);
                process.ppid = std::stoul(tokens[1]);
                process.name = tokens[2];
                process.eprocess_addr = ParseHexAddress(tokens[3]);
                process.threads = std::stoul(tokens[4]);
                process.handles = std::stoul(tokens[5]);
                // Session ID and WoW64 info in tokens[6] and [7]
                process.wow64 = (tokens[7] == "True");
                
                // Create time might span multiple tokens
                if (tokens.size() > 8) {
                    process.create_time = tokens[8];
                    if (tokens.size() > 9) {
                        process.create_time += " " + tokens[9];
                    }
                }

                m_processes.push_back(process);
            } catch (const std::exception&) {
                // Skip malformed lines
                continue;
            }
        }
    }

    return !m_processes.empty();
}

std::vector<ProcessEntry> MemoryAnalyzer::FindProcessByName(const std::string& name) const {
    std::vector<ProcessEntry> matches;
    std::string lower_name = name;
    std::transform(lower_name.begin(), lower_name.end(), lower_name.begin(), ::tolower);

    for (const auto& process : m_processes) {
        std::string lower_proc_name = process.name;
        std::transform(lower_proc_name.begin(), lower_proc_name.end(), lower_proc_name.begin(), ::tolower);
        
        if (lower_proc_name.find(lower_name) != std::string::npos) {
            matches.push_back(process);
        }
    }

    return matches;
}

ProcessEntry* MemoryAnalyzer::FindProcessByPID(uint32_t pid) {
    for (auto& process : m_processes) {
        if (process.pid == pid) {
            return &process;
        }
    }
    return nullptr;
}

std::vector<ModuleEntry> MemoryAnalyzer::GetProcessModules(uint32_t pid) {
    std::vector<ModuleEntry> modules;
    
    if (!m_volatility || m_current_memory_file.empty()) {
        return modules;
    }

    std::string modules_output = m_volatility->ListModules(m_current_memory_file, pid);
    ParseModuleList(modules_output, modules);
    
    return modules;
}

bool MemoryAnalyzer::ParseModuleList(const std::string& modules_output, std::vector<ModuleEntry>& modules) {
    modules.clear();
    
    std::istringstream iss(modules_output);
    std::string line;
    bool header_found = false;

    while (std::getline(iss, line)) {
        if (!header_found) {
            if (line.find("Base") != std::string::npos && line.find("Name") != std::string::npos) {
                header_found = true;
            }
            continue;
        }

        if (line.empty()) continue;

        std::istringstream line_stream(line);
        std::vector<std::string> tokens;
        std::string token;
        
        while (line_stream >> token) {
            tokens.push_back(token);
        }

        if (tokens.size() >= 3) {
            try {
                ModuleEntry module;
                module.base_address = ParseHexAddress(tokens[0]);
                module.size = std::stoul(tokens[1], nullptr, 16);
                module.name = tokens[2];
                
                modules.push_back(module);
            } catch (const std::exception&) {
                continue;
            }
        }
    }

    return !modules.empty();
}

bool MemoryAnalyzer::ReadProcessMemory(uint32_t pid, uint64_t address, void* buffer, size_t size) {
    if (!m_initialized || !m_dma_client) {
        SetError("Not initialized");
        return false;
    }

    // For now, read physical memory directly
    // In a full implementation, you'd need CR3 for virtual address translation
    return m_dma_client->ReadPhysicalMemory(address, buffer, size);
}

bool MemoryAnalyzer::WriteProcessMemory(uint32_t pid, uint64_t address, const void* buffer, size_t size) {
    if (!m_initialized || !m_dma_client) {
        SetError("Not initialized");
        return false;
    }

    return m_dma_client->WritePhysicalMemory(address, buffer, size);
}

uint64_t MemoryAnalyzer::TranslateProcessVirtualAddress(uint32_t pid, uint64_t virtual_address) {
    if (!m_initialized || !m_dma_client) {
        return 0;
    }

    // Find process to get CR3
    ProcessEntry* process = FindProcessByPID(pid);
    if (!process || process->cr3 == 0) {
        return 0;
    }

    return m_dma_client->TranslateVirtualAddress(process->cr3, virtual_address);
}

std::vector<MemoryRegion> MemoryAnalyzer::GetMemoryLayout() {
    return m_memory_regions;
}

bool MemoryAnalyzer::IsConnected() const {
    return m_initialized && m_dma_client && m_dma_client->IsConnected();
}

std::string MemoryAnalyzer::GetConnectionStatus() const {
    if (!m_initialized) {
        return "Not initialized";
    }
    if (!m_dma_client) {
        return "No DMA client";
    }
    if (m_dma_client->IsConnected()) {
        return "Connected to " + m_qemu_host + ":" + std::to_string(m_qemu_port);
    } else {
        return "Disconnected";
    }
}

std::string MemoryAnalyzer::GetAnalysisStatus() const {
    if (!m_memory_analyzed) {
        return "No memory analysis performed";
    }
    return "Analyzed " + std::to_string(m_processes.size()) + " processes from " + m_current_memory_file;
}

void MemoryAnalyzer::PrintStatistics() const {
    if (!m_dma_client) {
        std::cout << "No statistics available\n";
        return;
    }

    std::cout << "DMA Statistics:\n";
    std::cout << "  Bytes Read: " << FormatMemorySize(m_dma_client->GetBytesRead()) << "\n";
    std::cout << "  Bytes Written: " << FormatMemorySize(m_dma_client->GetBytesWritten()) << "\n";
    std::cout << "  Packets Processed: " << m_dma_client->GetPacketsProcessed() << "\n";
    std::cout << "  Processes Found: " << m_processes.size() << "\n";
    std::cout << "  Memory Regions: " << m_memory_regions.size() << "\n";
}

void MemoryAnalyzer::SetError(const std::string& error) {
    m_last_error = error;
}

// Utility functions
std::string FormatProcessInfo(const ProcessEntry& process) {
    char buffer[256];
    sprintf_s(buffer, sizeof(buffer), "PID %5u: %-20s (PPID: %5u, Threads: %3u, Handles: %4u)",
              process.pid, process.name.c_str(), process.ppid, process.threads, process.handles);
    return std::string(buffer);
}

std::string FormatModuleInfo(const ModuleEntry& module) {
    char buffer[256];
    sprintf_s(buffer, sizeof(buffer), "0x%016llX: %-30s (Size: %s)",
              static_cast<unsigned long long>(module.base_address),
              module.name.c_str(),
              FormatMemorySize(module.size).c_str());
    return std::string(buffer);
}

bool IsValidProcessName(const std::string& name) {
    return !name.empty() && name.length() < 256;
}

uint64_t ParseHexAddress(const std::string& addr_str) {
    try {
        return std::stoull(addr_str, nullptr, 16);
    } catch (...) {
        return 0;
    }
}

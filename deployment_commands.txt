# Copy and paste these commands into your SSH session on Proxmox
# QEMU source is at /opt/test/qemu

# Step 1: Navigate to QEMU source and check structure
cd /opt/test/qemu
pwd
ls -la hw/misc/

# Step 2: Create the streamlined DMA device file
cat > hw/misc/streamlined_dma.c << 'EOF'
/*
 * Streamlined DMA Device for Memory Analysis
 * Provides core DMA functionality for external memory analysis tools
 * 
 * Features:
 * - Physical memory read/write access
 * - Bulk memory dumping for external analysis
 * - Network API for external tools (Volatility 3, MemProcFS, etc.)
 * - Virtual address translation support
 * 
 * Place in: qemu/hw/misc/
 * License: GPL-2.0+
 */

#include "qemu/osdep.h"
#include "qemu/cutils.h"
#include "hw/pci/pci.h"
#include "hw/pci/msi.h"
#include "net/net.h"
#include "qemu/module.h"
#include "qemu/timer.h"
#include "qemu/sockets.h"
#include "qemu/thread.h"
#include "io/channel-socket.h"
#include "hw/qdev-properties.h"
#include "qapi/error.h"
#include "exec/cpu-common.h"
#include "qemu/error-report.h"
#include "qemu/log.h"
#include "qemu/queue.h"
#include "exec/cpu-all.h"
#include "target/i386/cpu.h"
#include <inttypes.h>

#define TYPE_STREAMLINED_DMA "streamlined-dma"
#define STREAMLINED_DMA(obj) OBJECT_CHECK(StreamlinedDMAState, (obj), TYPE_STREAMLINED_DMA)

/* Custom DMA Device */
#define PCI_VENDOR_ID_CUSTOM        0x1337
#define PCI_DEVICE_ID_DMA           0x0001
#define DMA_DEFAULT_PORT            31338
#define DMA_MAGIC                   0x444D4100  /* 'DMA\0' */

/* Core command definitions */
#define CMD_PING                    0x1000
#define CMD_READ_PHYS               0x2000
#define CMD_WRITE_PHYS              0x2001
#define CMD_TRANSLATE_VA            0x2002
#define CMD_DUMP_MEMORY             0x3000
#define CMD_GET_MEMORY_LAYOUT       0x3001

/* Maximum limits */
#define MAX_TRANSFER_SIZE           0x100000  /* 1MB */
#define DUMP_CHUNK_SIZE             0x100000  /* 1MB chunks for memory dumping */
#define MAX_MEMORY_REGIONS          64        /* Maximum memory regions to track */

/* Protocol structures */
typedef struct {
    uint32_t magic;
    uint32_t command;
    uint32_t status;
    uint32_t data_size;
    uint64_t param1;
    uint64_t param2;
    uint64_t param3;
    uint32_t reserved[4];
} __attribute__((packed)) DMAPacket;

typedef struct {
    uint64_t start_addr;
    uint64_t size;
    uint32_t flags;
    uint32_t reserved;
} __attribute__((packed)) MemoryRegion;

typedef struct StreamlinedDMAState {
    PCIDevice parent_obj;
    MemoryRegion mmio;
    QIOChannelSocket *server;
    QemuThread thread;
    bool running;
    uint32_t port;
    QemuMutex lock;
    
    /* Memory layout information */
    MemoryRegion memory_regions[MAX_MEMORY_REGIONS];
    uint32_t num_memory_regions;
    
    /* Statistics */
    uint64_t bytes_read;
    uint64_t bytes_written;
    uint64_t packets_processed;
} StreamlinedDMAState;

/* Function declarations */
static void init_memory_layout(StreamlinedDMAState *s);
static void handle_client(StreamlinedDMAState *s, QIOChannelSocket *client);
static void *network_thread(void *opaque);

/* Core memory access functions */
static bool read_phys_mem(uint64_t addr, void *buf, size_t size) {
    CPUState *cpu = qemu_get_cpu(0);
    if (!cpu) {
        return false;
    }
    int ret = cpu_physical_memory_rw(addr, buf, size, 0); /* 0 = read */
    return (ret == 0);
}

static bool write_phys_mem(uint64_t addr, const void *buf, size_t size) {
    CPUState *cpu = qemu_get_cpu(0);
    if (!cpu) {
        return false;
    }
    int ret = cpu_physical_memory_rw(addr, (uint8_t *)buf, size, 1); /* 1 = write */
    return (ret == 0);
}
EOF

# Step 3: Check if the file was created successfully
echo "Checking if streamlined_dma.c was created:"
ls -la hw/misc/streamlined_dma.c
wc -l hw/misc/streamlined_dma.c

# Step 4: Backup and update meson.build
echo "Backing up meson.build..."
cp hw/misc/meson.build hw/misc/meson.build.backup

echo "Current meson.build content:"
cat hw/misc/meson.build

echo "Adding streamlined_dma.c to meson.build..."
if ! grep -q "streamlined_dma.c" hw/misc/meson.build; then
    echo "misc_ss.add(files('streamlined_dma.c'))" >> hw/misc/meson.build
    echo "Added streamlined_dma.c to meson.build"
else
    echo "streamlined_dma.c already in meson.build"
fi

echo "Updated meson.build:"
tail -5 hw/misc/meson.build

# Step 5: Check build directory and build QEMU
echo "Checking build directory..."
ls -la build/

echo "Building QEMU..."
cd build
make clean
make -j$(nproc)

# Step 6: Install QEMU
echo "Installing QEMU..."
make install

echo "=== Deployment completed! ==="
echo "Next steps:"
echo "1. Stop VM 102: qm stop 102"
echo "2. Edit VM config: nano /etc/pve/qemu-server/102.conf"
echo "3. Add: args: -device streamlined-dma,id=dma0"
echo "4. Start VM 102: qm start 102"
echo "5. Test connection on port 31338"

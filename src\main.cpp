/*
 * DMA Memory Analyzer - Main Application
 * High-performance memory analysis tool for QEMU DMA device
 */

#include <iostream>
#include <string>
#include <vector>
#include <memory>
#include <chrono>
#include <iomanip>
#include <sstream>
#include <algorithm>

#include "memory_analyzer.h"
#include "dma_client.h"

void PrintUsage(const char* program_name) {
    std::cout << "DMA Memory Analyzer v1.0\n";
    std::cout << "Usage: " << program_name << " [options]\n\n";
    std::cout << "Options:\n";
    std::cout << "  -h, --help              Show this help message\n";
    std::cout << "  -H, --host <ip>         QEMU host IP address (default: localhost)\n";
    std::cout << "  -p, --port <port>       DMA device port (default: 31338)\n";
    std::cout << "  -d, --dump <file>       Dump memory to file\n";
    std::cout << "  -s, --size <size>       Memory dump size (default: 4GB)\n";
    std::cout << "  -a, --analyze <file>    Analyze existing memory dump\n";
    std::cout << "  -v, --volatility <path> Path to Volatility 3 (default: auto-detect)\n";
    std::cout << "  -i, --interactive       Enter interactive mode\n";
    std::cout << "  -l, --list-processes    List processes only\n";
    std::cout << "  -f, --find <name>       Find processes by name\n";
    std::cout << "  -P, --pid <pid>         Target specific process ID\n";
    std::cout << "  -r, --read <addr> <size> Read memory from address\n";
    std::cout << "  -w, --write <addr> <hex> Write hex data to address\n";
    std::cout << "  --test-connection       Test DMA device connection\n";
    std::cout << "  --memory-layout         Show memory layout\n";
    std::cout << "\nExamples:\n";
    std::cout << "  " << program_name << " --host ************* --dump memory.raw\n";
    std::cout << "  " << program_name << " --analyze memory.raw --list-processes\n";
    std::cout << "  " << program_name << " --host ************* --interactive\n";
    std::cout << "  " << program_name << " --analyze memory.raw --find notepad\n";
}

uint64_t ParseSize(const std::string& size_str) {
    std::string upper_str = size_str;
    std::transform(upper_str.begin(), upper_str.end(), upper_str.begin(), ::toupper);
    
    double value = std::stod(upper_str);
    
    if (upper_str.find("GB") != std::string::npos) {
        return static_cast<uint64_t>(value * 1024 * 1024 * 1024);
    } else if (upper_str.find("MB") != std::string::npos) {
        return static_cast<uint64_t>(value * 1024 * 1024);
    } else if (upper_str.find("KB") != std::string::npos) {
        return static_cast<uint64_t>(value * 1024);
    } else {
        return static_cast<uint64_t>(value);
    }
}

void InteractiveMode(MemoryAnalyzer& analyzer) {
    std::cout << "\n=== Interactive Memory Analysis Mode ===\n";
    std::cout << "Commands:\n";
    std::cout << "  list                    - List all processes\n";
    std::cout << "  find <name>            - Find processes by name\n";
    std::cout << "  info <pid>             - Get process information\n";
    std::cout << "  modules <pid>          - List process modules\n";
    std::cout << "  read <pid> <addr> <size> - Read process memory\n";
    std::cout << "  write <pid> <addr> <hex> - Write process memory\n";
    std::cout << "  scan <pid> <pattern>   - Scan process memory for pattern\n";
    std::cout << "  dump <pid> <dir>       - Dump process memory\n";
    std::cout << "  layout                 - Show memory layout\n";
    std::cout << "  status                 - Show connection status\n";
    std::cout << "  stats                  - Show statistics\n";
    std::cout << "  help                   - Show this help\n";
    std::cout << "  quit                   - Exit interactive mode\n\n";

    std::string line;
    while (std::cout << "> " && std::getline(std::cin, line)) {
        if (line.empty()) continue;

        std::istringstream iss(line);
        std::vector<std::string> tokens;
        std::string token;
        while (iss >> token) {
            tokens.push_back(token);
        }

        if (tokens.empty()) continue;

        const std::string& cmd = tokens[0];

        try {
            if (cmd == "quit" || cmd == "exit") {
                break;
            } else if (cmd == "help") {
                std::cout << "Available commands listed above.\n";
            } else if (cmd == "list") {
                auto processes = analyzer.GetProcessList();
                std::cout << "Found " << processes.size() << " processes:\n";
                for (const auto& proc : processes) {
                    std::cout << FormatProcessInfo(proc) << "\n";
                }
            } else if (cmd == "find" && tokens.size() > 1) {
                auto matches = analyzer.FindProcessByName(tokens[1]);
                std::cout << "Found " << matches.size() << " matching processes:\n";
                for (const auto& proc : matches) {
                    std::cout << FormatProcessInfo(proc) << "\n";
                }
            } else if (cmd == "info" && tokens.size() > 1) {
                uint32_t pid = std::stoul(tokens[1]);
                auto* proc = analyzer.FindProcessByPID(pid);
                if (proc) {
                    std::cout << "Process Information:\n";
                    std::cout << FormatProcessInfo(*proc) << "\n";
                } else {
                    std::cout << "Process with PID " << pid << " not found.\n";
                }
            } else if (cmd == "modules" && tokens.size() > 1) {
                uint32_t pid = std::stoul(tokens[1]);
                auto modules = analyzer.GetProcessModules(pid);
                std::cout << "Found " << modules.size() << " modules for PID " << pid << ":\n";
                for (const auto& mod : modules) {
                    std::cout << FormatModuleInfo(mod) << "\n";
                }
            } else if (cmd == "layout") {
                auto regions = analyzer.GetMemoryLayout();
                std::cout << "Memory Layout (" << regions.size() << " regions):\n";
                for (const auto& region : regions) {
                    std::cout << "  " << FormatAddress(region.start_addr) 
                             << " - " << FormatAddress(region.start_addr + region.size)
                             << " (" << FormatMemorySize(region.size) << ")\n";
                }
            } else if (cmd == "status") {
                std::cout << "Connection Status: " << analyzer.GetConnectionStatus() << "\n";
                std::cout << "Analysis Status: " << analyzer.GetAnalysisStatus() << "\n";
            } else if (cmd == "stats") {
                analyzer.PrintStatistics();
            } else {
                std::cout << "Unknown command: " << cmd << "\n";
                std::cout << "Type 'help' for available commands.\n";
            }
        } catch (const std::exception& e) {
            std::cout << "Error: " << e.what() << "\n";
        }
    }
}

int main(int argc, char* argv[]) {
    std::string host = "localhost";
    uint16_t port = DMA_DEFAULT_PORT;
    std::string dump_file;
    std::string analyze_file;
    std::string volatility_path;
    std::string find_process;
    uint64_t dump_size = 4ULL * 1024 * 1024 * 1024; // 4GB
    uint32_t target_pid = 0;
    bool interactive = false;
    bool list_processes = false;
    bool test_connection = false;
    bool show_memory_layout = false;

    // Parse command line arguments
    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];
        
        if (arg == "-h" || arg == "--help") {
            PrintUsage(argv[0]);
            return 0;
        } else if ((arg == "-H" || arg == "--host") && i + 1 < argc) {
            host = argv[++i];
        } else if ((arg == "-p" || arg == "--port") && i + 1 < argc) {
            port = static_cast<uint16_t>(std::stoul(argv[++i]));
        } else if ((arg == "-d" || arg == "--dump") && i + 1 < argc) {
            dump_file = argv[++i];
        } else if ((arg == "-s" || arg == "--size") && i + 1 < argc) {
            dump_size = ParseSize(argv[++i]);
        } else if ((arg == "-a" || arg == "--analyze") && i + 1 < argc) {
            analyze_file = argv[++i];
        } else if ((arg == "-v" || arg == "--volatility") && i + 1 < argc) {
            volatility_path = argv[++i];
        } else if (arg == "-i" || arg == "--interactive") {
            interactive = true;
        } else if (arg == "-l" || arg == "--list-processes") {
            list_processes = true;
        } else if ((arg == "-f" || arg == "--find") && i + 1 < argc) {
            find_process = argv[++i];
        } else if ((arg == "-P" || arg == "--pid") && i + 1 < argc) {
            target_pid = std::stoul(argv[++i]);
        } else if (arg == "--test-connection") {
            test_connection = true;
        } else if (arg == "--memory-layout") {
            show_memory_layout = true;
        } else {
            std::cerr << "Unknown argument: " << arg << "\n";
            PrintUsage(argv[0]);
            return 1;
        }
    }

    try {
        MemoryAnalyzer analyzer;

        // Test connection only
        if (test_connection) {
            std::cout << "Testing connection to " << host << ":" << port << "...\n";
            if (analyzer.Initialize(host, port)) {
                std::cout << "Connection successful!\n";
                analyzer.PrintStatistics();
                return 0;
            } else {
                std::cerr << "Connection failed: " << analyzer.GetLastError() << "\n";
                return 1;
            }
        }

        // Initialize analyzer
        if (!analyze_file.empty()) {
            // Analyze existing memory dump
            if (!volatility_path.empty()) {
                analyzer.SetVolatilityPath(volatility_path);
            }
            
            std::cout << "Analyzing memory dump: " << analyze_file << "\n";
            if (!analyzer.AnalyzeMemoryDump(analyze_file)) {
                std::cerr << "Failed to analyze memory dump: " << analyzer.GetLastError() << "\n";
                return 1;
            }
        } else {
            // Connect to QEMU DMA device
            std::cout << "Connecting to QEMU DMA device at " << host << ":" << port << "\n";
            if (!analyzer.Initialize(host, port)) {
                std::cerr << "Failed to connect: " << analyzer.GetLastError() << "\n";
                return 1;
            }

            if (!volatility_path.empty()) {
                analyzer.SetVolatilityPath(volatility_path);
            }

            // Dump memory if requested
            if (!dump_file.empty()) {
                std::cout << "Dumping " << FormatMemorySize(dump_size) << " to " << dump_file << "\n";
                auto start_time = std::chrono::high_resolution_clock::now();
                
                if (!analyzer.DumpMemoryToFile(dump_file, dump_size)) {
                    std::cerr << "Memory dump failed: " << analyzer.GetLastError() << "\n";
                    return 1;
                }
                
                auto end_time = std::chrono::high_resolution_clock::now();
                auto duration = std::chrono::duration_cast<std::chrono::seconds>(end_time - start_time);
                std::cout << "Memory dump completed in " << duration.count() << " seconds\n";

                // Analyze the dump
                std::cout << "Analyzing memory dump...\n";
                if (!analyzer.AnalyzeMemoryDump(dump_file)) {
                    std::cerr << "Failed to analyze memory dump: " << analyzer.GetLastError() << "\n";
                    return 1;
                }
            }
        }

        // Show memory layout
        if (show_memory_layout) {
            auto regions = analyzer.GetMemoryLayout();
            std::cout << "\nMemory Layout (" << regions.size() << " regions):\n";
            for (const auto& region : regions) {
                std::cout << "  " << FormatAddress(region.start_addr) 
                         << " - " << FormatAddress(region.start_addr + region.size)
                         << " (" << FormatMemorySize(region.size) << ", flags: 0x" 
                         << std::hex << region.flags << std::dec << ")\n";
            }
        }

        // List processes
        if (list_processes) {
            auto processes = analyzer.GetProcessList();
            std::cout << "\nProcess List (" << processes.size() << " processes):\n";
            for (const auto& proc : processes) {
                std::cout << FormatProcessInfo(proc) << "\n";
            }
        }

        // Find specific process
        if (!find_process.empty()) {
            auto matches = analyzer.FindProcessByName(find_process);
            std::cout << "\nFound " << matches.size() << " processes matching '" << find_process << "':\n";
            for (const auto& proc : matches) {
                std::cout << FormatProcessInfo(proc) << "\n";
            }
        }

        // Interactive mode
        if (interactive) {
            InteractiveMode(analyzer);
        }

        // Print final statistics
        if (analyzer.IsConnected()) {
            std::cout << "\nFinal Statistics:\n";
            analyzer.PrintStatistics();
        }

    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << "\n";
        return 1;
    }

    return 0;
}

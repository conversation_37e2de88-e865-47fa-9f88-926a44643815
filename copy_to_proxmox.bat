@echo off
REM Copy streamlined DMA device to Proxmox server
REM Update these variables with your Proxmox details

echo === Copying Streamlined DMA Device to Proxmox ===

REM Default values - update these
set PROXMOX_HOST=your_proxmox_ip
set PROXMOX_USER=root
set QEMU_SOURCE_PATH=/usr/src/qemu

echo.
echo Please update the variables in this script with your Proxmox details:
echo PROXMOX_HOST=%PROXMOX_HOST%
echo PROXMOX_USER=%PROXMOX_USER%
echo QEMU_SOURCE_PATH=%QEMU_SOURCE_PATH%
echo.

if "%PROXMOX_HOST%"=="your_proxmox_ip" (
    echo Error: Please update PROXMOX_HOST in this script
    pause
    exit /b 1
)

echo Copying streamlined_dma.c to Proxmox...
scp example_qemu\hw\misc\streamlined_dma.c %PROXMOX_USER%@%PROXMOX_HOST%:%QEMU_SOURCE_PATH%/hw/misc/

if %ERRORLEVEL% neq 0 (
    echo Error: Failed to copy streamlined_dma.c
    pause
    exit /b 1
)

echo Copying deployment script...
scp deploy_commands.sh %PROXMOX_USER%@%PROXMOX_HOST%:%QEMU_SOURCE_PATH%/

if %ERRORLEVEL% neq 0 (
    echo Error: Failed to copy deploy_commands.sh
    pause
    exit /b 1
)

echo.
echo Files copied successfully!
echo.
echo Next steps:
echo 1. SSH to Proxmox: ssh %PROXMOX_USER%@%PROXMOX_HOST%
echo 2. Go to QEMU source: cd %QEMU_SOURCE_PATH%
echo 3. Run deployment: chmod +x deploy_commands.sh && ./deploy_commands.sh
echo.
pause

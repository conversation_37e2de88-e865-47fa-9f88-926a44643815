/*
 * Streamlined DMA Device for Memory Analysis
 * Provides core DMA functionality for external memory analysis tools
 * 
 * Features:
 * - Physical memory read/write access
 * - Bulk memory dumping for external analysis
 * - Network API for external tools (Volatility 3, MemProcFS, etc.)
 * - Virtual address translation support
 * 
 * Place in: qemu/hw/misc/
 * License: GPL-2.0+
 */

#include "qemu/osdep.h"
#include "qemu/cutils.h"
#include "hw/pci/pci.h"
#include "hw/pci/msi.h"
#include "net/net.h"
#include "qemu/module.h"
#include "qemu/timer.h"
#include "qemu/sockets.h"
#include "qemu/thread.h"
#include "io/channel-socket.h"
#include "hw/qdev-properties.h"
#include "qapi/error.h"
#include "exec/cpu-common.h"
#include "qemu/error-report.h"
#include "qemu/log.h"
#include "qemu/queue.h"
#include "exec/cpu-all.h"
#include "target/i386/cpu.h"
#include "cpu_physical_memory.h"
#include <inttypes.h>

#define TYPE_STREAMLINED_DMA "streamlined-dma"
#define STREAMLINED_DMA(obj) OBJECT_CHECK(StreamlinedDMAState, (obj), TYPE_STREAMLINED_DMA)

/* Custom DMA Device */
#define PCI_VENDOR_ID_CUSTOM        0x1337
#define PCI_DEVICE_ID_DMA           0x0001
#define DMA_DEFAULT_PORT            31338
#define DMA_MAGIC                   0x444D4100  /* 'DMA\0' */

/* Core command definitions */
#define CMD_PING                    0x1000
#define CMD_READ_PHYS               0x2000
#define CMD_WRITE_PHYS              0x2001
#define CMD_TRANSLATE_VA            0x2002
#define CMD_DUMP_MEMORY             0x3000
#define CMD_GET_MEMORY_LAYOUT       0x3001

/* Maximum limits */
#define MAX_TRANSFER_SIZE           0x100000  /* 1MB */
#define DUMP_CHUNK_SIZE             0x100000  /* 1MB chunks for memory dumping */
#define MAX_MEMORY_REGIONS          64        /* Maximum memory regions to track */

/* Protocol structures */
typedef struct {
    uint32_t magic;
    uint32_t command;
    uint32_t status;
    uint32_t data_size;
    uint64_t param1;
    uint64_t param2;
    uint64_t param3;
    uint32_t reserved[4];
} __attribute__((packed)) DMAPacket;

typedef struct {
    uint64_t start_addr;
    uint64_t size;
    uint32_t flags;
    uint32_t reserved;
} __attribute__((packed)) MemoryRegion;

typedef struct StreamlinedDMAState {
    PCIDevice parent_obj;
    MemoryRegion mmio;
    QIOChannelSocket *server;
    QemuThread thread;
    bool running;
    uint32_t port;
    QemuMutex lock;
    
    /* Memory layout information */
    MemoryRegion memory_regions[MAX_MEMORY_REGIONS];
    uint32_t num_memory_regions;
    
    /* Statistics */
    uint64_t bytes_read;
    uint64_t bytes_written;
    uint64_t packets_processed;
} StreamlinedDMAState;

/* Function declarations */
static void init_memory_layout(StreamlinedDMAState *s);
static void handle_client(StreamlinedDMAState *s, QIOChannelSocket *client);
static void *network_thread(void *opaque);

/* Core memory access functions - optimized and safe */
static bool read_phys_mem(uint64_t addr, void *buf, size_t size) {
    if (!buf || size == 0 || size > MAX_TRANSFER_SIZE) {
        return false;
    }

    /* Validate address range to prevent crashes */
    if (addr > 0xFFFFFFFFFFFFULL || (addr + size) < addr) {
        return false; /* Address overflow */
    }

    /* Use direct memory access for better performance */
    MemTxResult result = address_space_read(&address_space_memory, addr,
                                          MEMTXATTRS_UNSPECIFIED, buf, size);
    return result == MEMTX_OK;
}

static bool write_phys_mem(uint64_t addr, const void *buf, size_t size) {
    if (!buf || size == 0 || size > MAX_TRANSFER_SIZE) {
        return false;
    }

    /* Validate address range */
    if (addr > 0xFFFFFFFFFFFFULL || (addr + size) < addr) {
        return false; /* Address overflow */
    }

    /* Use direct memory access for better performance */
    MemTxResult result = address_space_write(&address_space_memory, addr,
                                           MEMTXATTRS_UNSPECIFIED, buf, size);
    return result == MEMTX_OK;
}

/* Page table walking for virtual address translation */
static uint64_t translate_virtual_address(uint64_t cr3, uint64_t va) {
    uint64_t pml4_index = (va >> 39) & 0x1FF;
    uint64_t pdpt_index = (va >> 30) & 0x1FF;
    uint64_t pd_index = (va >> 21) & 0x1FF;
    uint64_t pt_index = (va >> 12) & 0x1FF;
    uint64_t page_offset = va & 0xFFF;
    
    /* PML4 (Page Map Level 4) */
    uint64_t pml4_base = cr3 & 0xFFFFFFFFFFFFF000ULL;
    uint64_t pml4_entry;
    if (!read_phys_mem(pml4_base + pml4_index * 8, &pml4_entry, 8) || !(pml4_entry & 1)) {
        return 0;
    }
    
    /* PDPT (Page Directory Pointer Table) */
    uint64_t pdpt_base = pml4_entry & 0xFFFFFFFFFFFFF000ULL;
    uint64_t pdpt_entry;
    if (!read_phys_mem(pdpt_base + pdpt_index * 8, &pdpt_entry, 8) || !(pdpt_entry & 1)) {
        return 0;
    }
    
    /* Check for 1GB page */
    if (pdpt_entry & 0x80) {
        return (pdpt_entry & 0xFFFFFFFFC0000000ULL) | (va & 0x3FFFFFFFULL);
    }
    
    /* PD (Page Directory) */
    uint64_t pd_base = pdpt_entry & 0xFFFFFFFFFFFFF000ULL;
    uint64_t pd_entry;
    if (!read_phys_mem(pd_base + pd_index * 8, &pd_entry, 8) || !(pd_entry & 1)) {
        return 0;
    }
    
    /* Check for 2MB page */
    if (pd_entry & 0x80) {
        return (pd_entry & 0xFFFFFFFFFFFE0000ULL) | (va & 0x1FFFFFULL);
    }
    
    /* PT (Page Table) */
    uint64_t pt_base = pd_entry & 0xFFFFFFFFFFFFF000ULL;
    uint64_t pt_entry;
    if (!read_phys_mem(pt_base + pt_index * 8, &pt_entry, 8) || !(pt_entry & 1)) {
        return 0;
    }
    
    return (pt_entry & 0xFFFFFFFFFFFFF000ULL) | page_offset;
}

/* Initialize memory layout information */
static void init_memory_layout(StreamlinedDMAState *s) {
    qemu_log("StreamlinedDMA: Initializing memory layout information\n");
    
    /* Initialize basic memory regions for common system layouts */
    s->num_memory_regions = 0;
    
    /* Low memory region (0-1MB) */
    s->memory_regions[s->num_memory_regions].start_addr = 0x0;
    s->memory_regions[s->num_memory_regions].size = 0x100000;
    s->memory_regions[s->num_memory_regions].flags = 0x1; /* System */
    s->num_memory_regions++;
    
    /* Standard RAM regions (1MB-4GB) */
    s->memory_regions[s->num_memory_regions].start_addr = 0x100000;
    s->memory_regions[s->num_memory_regions].size = 0xFFF00000;
    s->memory_regions[s->num_memory_regions].flags = 0x2; /* RAM */
    s->num_memory_regions++;
    
    /* High memory region (4GB+) - will be dynamically detected */
    s->memory_regions[s->num_memory_regions].start_addr = 0x100000000ULL;
    s->memory_regions[s->num_memory_regions].size = 0x400000000ULL; /* 16GB max */
    s->memory_regions[s->num_memory_regions].flags = 0x2; /* RAM */
    s->num_memory_regions++;
    
    qemu_log("StreamlinedDMA: Initialized %d memory regions\n", s->num_memory_regions);
}

/* Handle client connection and process commands - optimized and safe */
static void handle_client(StreamlinedDMAState *s, QIOChannelSocket *client) {
    DMAPacket packet;
    Error *err = NULL;
    uint32_t consecutive_errors = 0;
    const uint32_t MAX_CONSECUTIVE_ERRORS = 5;

    qemu_log("StreamlinedDMA: Client connected from %s\n",
             qio_channel_socket_get_remote_address(client, &err) ?: "unknown");

    /* Set socket options for performance */
    qio_channel_set_blocking(QIO_CHANNEL(client), true, NULL);

    while (true) {
        /* Clear packet structure */
        memset(&packet, 0, sizeof(packet));

        /* Receive packet header with timeout */
        if (qio_channel_read_all(QIO_CHANNEL(client), (char *)&packet, sizeof(packet), &err) < 0 || err) {
            if (err) {
                qemu_log("StreamlinedDMA: Client read error: %s\n", error_get_pretty(err));
                error_free(err);
                err = NULL;
            } else {
                qemu_log("StreamlinedDMA: Client disconnected gracefully\n");
            }
            break;
        }

        /* Validate magic number */
        if (packet.magic != DMA_MAGIC) {
            qemu_log("StreamlinedDMA: Invalid magic number: 0x%08X (expected 0x%08X)\n",
                     packet.magic, DMA_MAGIC);
            consecutive_errors++;
            if (consecutive_errors >= MAX_CONSECUTIVE_ERRORS) {
                qemu_log("StreamlinedDMA: Too many consecutive errors, disconnecting client\n");
                break;
            }
            continue;
        }

        /* Validate command */
        if (packet.command < CMD_PING || packet.command > CMD_GET_MEMORY_LAYOUT) {
            qemu_log("StreamlinedDMA: Invalid command: 0x%04X\n", packet.command);
            consecutive_errors++;
            if (consecutive_errors >= MAX_CONSECUTIVE_ERRORS) {
                break;
            }
            continue;
        }

        consecutive_errors = 0; /* Reset error counter on valid packet */
        qemu_log("StreamlinedDMA: Processing command 0x%04X\n", packet.command);
        s->packets_processed++;

        /* Prepare response with proper initialization */
        DMAPacket response;
        memset(&response, 0, sizeof(response));
        response.magic = DMA_MAGIC;
        response.command = packet.command;
        response.status = 1;  /* Default to error */

        /* Process commands */
        switch (packet.command) {
        case CMD_PING:
            qemu_log("StreamlinedDMA: Processing PING command\n");
            response.status = 0;
            response.param1 = 0x504F4E47; /* 'PONG' */
            break;

        case CMD_READ_PHYS: {
            uint64_t address = packet.param1;
            uint32_t size = (uint32_t)packet.param2;

            if (size == 0 || size > MAX_TRANSFER_SIZE) {
                qemu_log("StreamlinedDMA: Invalid read size: 0x%X (max: 0x%X)\n", size, MAX_TRANSFER_SIZE);
                response.status = 2; /* Invalid size */
                break;
            }

            /* Allocate buffer with alignment for better performance */
            uint8_t *buffer = g_malloc0(size);
            if (!buffer) {
                qemu_log("StreamlinedDMA: Failed to allocate %u bytes\n", size);
                response.status = 3; /* Memory allocation failed */
                break;
            }

            if (read_phys_mem(address, buffer, size)) {
                response.status = 0;
                response.data_size = size;
                s->bytes_read += size;

                /* Send response header first */
                if (qio_channel_write_all(QIO_CHANNEL(client), (char *)&response, sizeof(response), &err) >= 0) {
                    /* Send data in optimized chunks */
                    if (qio_channel_write_all(QIO_CHANNEL(client), (char *)buffer, size, &err) < 0) {
                        qemu_log("StreamlinedDMA: Failed to send data: %s\n",
                                err ? error_get_pretty(err) : "unknown error");
                        if (err) error_free(err);
                    }
                } else {
                    qemu_log("StreamlinedDMA: Failed to send response header\n");
                    if (err) error_free(err);
                }
                g_free(buffer);
                continue; /* Skip sending response again */
            } else {
                qemu_log("StreamlinedDMA: Physical memory read failed at 0x%016" PRIx64 "\n", address);
                response.status = 1;
                g_free(buffer);
            }
            break;
        }

        case CMD_WRITE_PHYS: {
            uint64_t address = packet.param1;
            uint32_t size = (uint32_t)packet.param2;
            qemu_log("StreamlinedDMA: WRITE_PHYS: addr=0x%016" PRIx64 ", size=0x%X\n", address, size);

            if (size > 0 && size <= MAX_TRANSFER_SIZE && packet.data_size == size) {
                uint8_t *buffer = g_malloc(size);
                if (qio_channel_read_all(QIO_CHANNEL(client), (char *)buffer, size, &err) >= 0) {
                    if (write_phys_mem(address, buffer, size)) {
                        response.status = 0;
                        s->bytes_written += size;
                        qemu_log("StreamlinedDMA: Physical memory write successful\n");
                    } else {
                        qemu_log("StreamlinedDMA: Physical memory write failed\n");
                        response.status = 1;
                    }
                } else {
                    qemu_log("StreamlinedDMA: Failed to read write data from client\n");
                    response.status = 3;
                }
                g_free(buffer);
            } else {
                qemu_log("StreamlinedDMA: Invalid write parameters\n");
                response.status = 2;
            }
            break;
        }

        case CMD_TRANSLATE_VA: {
            uint64_t va = packet.param1;
            uint64_t cr3 = packet.param2;
            qemu_log("StreamlinedDMA: TRANSLATE_VA: va=0x%016" PRIx64 ", cr3=0x%016" PRIx64 "\n", va, cr3);

            uint64_t phys_addr = translate_virtual_address(cr3, va);
            if (phys_addr) {
                response.status = 0;
                response.param1 = phys_addr;
                qemu_log("StreamlinedDMA: Translation successful: 0x%016" PRIx64 " -> 0x%016" PRIx64 "\n", va, phys_addr);
            } else {
                qemu_log("StreamlinedDMA: Translation failed\n");
                response.status = 1;
            }
            break;
        }

        case CMD_DUMP_MEMORY: {
            uint64_t start_addr = packet.param1;
            uint64_t size = packet.param2;

            /* Validate dump parameters */
            if (size == 0 || size > 0x400000000ULL) { /* Max 16GB dump */
                qemu_log("StreamlinedDMA: Invalid dump size: 0x%016" PRIx64 "\n", size);
                response.status = 2;
                break;
            }

            /* Check for address overflow */
            if (start_addr > 0xFFFFFFFFFFFFULL || (start_addr + size) < start_addr) {
                qemu_log("StreamlinedDMA: Invalid address range\n");
                response.status = 2;
                break;
            }

            qemu_log("StreamlinedDMA: DUMP_MEMORY: start=0x%016" PRIx64 ", size=%s\n",
                     start_addr, FormatMemorySize(size));

            response.status = 0;
            response.param1 = size;
            response.data_size = 0; /* Will send in chunks */

            /* Send response header first */
            if (qio_channel_write_all(QIO_CHANNEL(client), (char *)&response, sizeof(response), &err) < 0) {
                qemu_log("StreamlinedDMA: Failed to send dump response header\n");
                if (err) error_free(err);
                break;
            }

            /* Pre-allocate buffer for better performance */
            uint8_t *buffer = g_malloc(DUMP_CHUNK_SIZE);
            if (!buffer) {
                qemu_log("StreamlinedDMA: Failed to allocate dump buffer\n");
                break;
            }

            /* Send memory in optimized chunks */
            uint64_t remaining = size;
            uint64_t current_addr = start_addr;
            uint64_t total_sent = 0;

            while (remaining > 0 && s->running) {
                uint32_t chunk_size = (remaining > DUMP_CHUNK_SIZE) ? DUMP_CHUNK_SIZE : (uint32_t)remaining;

                if (read_phys_mem(current_addr, buffer, chunk_size)) {
                    /* Memory read successful */
                    if (qio_channel_write_all(QIO_CHANNEL(client), (char *)buffer, chunk_size, &err) < 0) {
                        qemu_log("StreamlinedDMA: Failed to send chunk at 0x%016" PRIx64 "\n", current_addr);
                        if (err) error_free(err);
                        break;
                    }
                    s->bytes_read += chunk_size;
                } else {
                    /* Send zeros for unreadable memory regions */
                    memset(buffer, 0, chunk_size);
                    if (qio_channel_write_all(QIO_CHANNEL(client), (char *)buffer, chunk_size, &err) < 0) {
                        qemu_log("StreamlinedDMA: Failed to send zero chunk\n");
                        if (err) error_free(err);
                        break;
                    }
                }

                current_addr += chunk_size;
                remaining -= chunk_size;
                total_sent += chunk_size;

                /* Log progress for large dumps */
                if (total_sent % (256 * 1024 * 1024) == 0) { /* Every 256MB */
                    qemu_log("StreamlinedDMA: Dump progress: %s / %s\n",
                             FormatMemorySize(total_sent), FormatMemorySize(size));
                }
            }

            g_free(buffer);
            qemu_log("StreamlinedDMA: Memory dump completed: %s\n", FormatMemorySize(total_sent));
            continue; /* Skip sending response again */
        }

        case CMD_GET_MEMORY_LAYOUT: {
            qemu_log("StreamlinedDMA: GET_MEMORY_LAYOUT\n");
            response.status = 0;
            response.data_size = s->num_memory_regions * sizeof(MemoryRegion);
            response.param1 = s->num_memory_regions;

            /* Send response header */
            qio_channel_write_all(QIO_CHANNEL(client), (char *)&response, sizeof(response), &err);
            /* Send memory regions */
            qio_channel_write_all(QIO_CHANNEL(client), (char *)s->memory_regions, response.data_size, &err);
            continue; /* Skip sending response again */
        }

        default:
            qemu_log("StreamlinedDMA: Unknown command: 0x%04X\n", packet.command);
            response.status = 0xFF; /* Unknown command */
            break;
        }

        /* Send response for commands that don't send data */
        qio_channel_write_all(QIO_CHANNEL(client), (char *)&response, sizeof(response), &err);
        if (err) {
            qemu_log("StreamlinedDMA: Failed to send response: %s\n", error_get_pretty(err));
            error_free(err);
            break;
        }
    }

    object_unref(OBJECT(client));
    qemu_log("StreamlinedDMA: Client handler finished\n");
}

/* Network thread function */
static void *network_thread(void *opaque) {
    StreamlinedDMAState *s = (StreamlinedDMAState *)opaque;
    SocketAddress addr = { .type = SOCKET_ADDRESS_TYPE_INET };
    addr.u.inet.host = g_strdup("0.0.0.0");
    addr.u.inet.port = g_strdup_printf("%d", s->port);

    QIOChannelSocket *listener = qio_channel_socket_new();
    Error *err = NULL;

    qemu_log("StreamlinedDMA: Starting network thread on port %d\n", s->port);

    if (qio_channel_socket_listen_sync(listener, &addr, 1, &err) < 0) {
        error_report("StreamlinedDMA: Failed to listen on 0.0.0.0:%d", s->port);
        if (err) {
            error_report("StreamlinedDMA: Listen error: %s", error_get_pretty(err));
            error_free(err);
        }
        g_free(addr.u.inet.host);
        g_free(addr.u.inet.port);
        object_unref(OBJECT(listener));
        return NULL;
    }

    error_report("StreamlinedDMA: Successfully listening on 0.0.0.0:%d", s->port);
    qemu_log("StreamlinedDMA: Ready to accept connections\n");

    while (s->running) {
        QIOChannelSocket *client = qio_channel_socket_accept(listener, &err);
        if (!client) {
            if (err) {
                if (s->running) { /* Only log if we're supposed to be running */
                    qemu_log("StreamlinedDMA: Accept error: %s\n", error_get_pretty(err));
                }
                error_free(err);
                err = NULL;
            }
            /* Small delay to prevent busy waiting */
            g_usleep(10000); /* 10ms */
            continue;
        }

        qemu_log("StreamlinedDMA: New client connection accepted\n");
        handle_client(s, client);
    }

    object_unref(OBJECT(listener));
    g_free(addr.u.inet.host);
    g_free(addr.u.inet.port);

    qemu_log("StreamlinedDMA: Network thread shutting down\n");
    return NULL;
}

/* Device initialization */
static void streamlined_dma_realize(PCIDevice *pci_dev, Error **errp) {
    StreamlinedDMAState *s = STREAMLINED_DMA(pci_dev);

    /* Initialize MMIO region (required for proper PCI device) */
    memory_region_init_io(&s->mmio, OBJECT(s), NULL, s, "streamlined-dma-mmio", 0x1000);
    pci_register_bar(pci_dev, 0, PCI_BASE_ADDRESS_SPACE_MEMORY, &s->mmio);

    /* Initialize state */
    s->port = DMA_DEFAULT_PORT;
    s->running = true;
    s->bytes_read = 0;
    s->bytes_written = 0;
    s->packets_processed = 0;
    qemu_mutex_init(&s->lock);

    /* Initialize memory layout */
    init_memory_layout(s);

    /* Start network thread */
    qemu_thread_create(&s->thread, "streamlined-dma-net", network_thread, s, QEMU_THREAD_JOINABLE);

    qemu_log("StreamlinedDMA: Device initialized on port %d\n", s->port);
}

/* Device cleanup */
static void streamlined_dma_exit(PCIDevice *pci_dev) {
    StreamlinedDMAState *s = STREAMLINED_DMA(pci_dev);

    s->running = false;

    /* Wait for network thread */
    qemu_thread_join(&s->thread);
    qemu_mutex_destroy(&s->lock);

    qemu_log("StreamlinedDMA: Device cleaned up\n");
    qemu_log("StreamlinedDMA: Statistics - Packets: %" PRIu64 ", Read: %" PRIu64 " bytes, Written: %" PRIu64 " bytes\n",
             s->packets_processed, s->bytes_read, s->bytes_written);
}

/* Device class initialization */
static void streamlined_dma_class_init(ObjectClass *klass, const void *data) {
    DeviceClass *dc = DEVICE_CLASS(klass);
    PCIDeviceClass *k = PCI_DEVICE_CLASS(klass);

    k->realize = streamlined_dma_realize;
    k->exit = streamlined_dma_exit;
    k->vendor_id = PCI_VENDOR_ID_CUSTOM;
    k->device_id = PCI_DEVICE_ID_DMA;
    k->revision = 0x01;
    k->class_id = PCI_CLASS_SYSTEM_OTHER;

    dc->desc = "Streamlined DMA Device for Memory Analysis";
    set_bit(DEVICE_CATEGORY_MISC, dc->categories);
}

/* Type information */
static const TypeInfo streamlined_dma_info = {
    .name = TYPE_STREAMLINED_DMA,
    .parent = TYPE_PCI_DEVICE,
    .instance_size = sizeof(StreamlinedDMAState),
    .class_init = streamlined_dma_class_init,
    .interfaces = (InterfaceInfo[]) {
        { INTERFACE_CONVENTIONAL_PCI_DEVICE },
        { },
    },
};

/* Register device type */
static void streamlined_dma_register_types(void) {
    type_register_static(&streamlined_dma_info);
}

type_init(streamlined_dma_register_types)

/* Utility function for formatting memory sizes */
static const char* FormatMemorySize(uint64_t size) {
    static char buffer[64];
    const char* units[] = {"B", "KB", "MB", "GB", "TB"};
    int unit_index = 0;
    double formatted_size = (double)size;

    while (formatted_size >= 1024.0 && unit_index < 4) {
        formatted_size /= 1024.0;
        unit_index++;
    }

    if (unit_index == 0) {
        snprintf(buffer, sizeof(buffer), "%.0f %s", formatted_size, units[unit_index]);
    } else {
        snprintf(buffer, sizeof(buffer), "%.2f %s", formatted_size, units[unit_index]);
    }

    return buffer;
}

#pragma once
/*
 * Memory Analyzer Interface
 * High-level memory analysis and process enumeration
 */

#include "dma_client.h"
#include "volatility_interface.h"
#include <vector>
#include <map>
#include <string>
#include <memory>

struct ProcessEntry {
    uint32_t pid;
    uint32_t ppid;
    std::string name;
    uint64_t eprocess_addr;
    uint64_t cr3;
    uint64_t peb_addr;
    uint32_t threads;
    uint32_t handles;
    std::string create_time;
    bool wow64;
    
    ProcessEntry() : pid(0), ppid(0), eprocess_addr(0), cr3(0), peb_addr(0), 
                    threads(0), handles(0), wow64(false) {}
};

struct ModuleEntry {
    std::string name;
    uint64_t base_address;
    uint32_t size;
    uint64_t entry_point;
    
    ModuleEntry() : base_address(0), size(0), entry_point(0) {}
};

class MemoryAnalyzer {
public:
    MemoryAnalyzer();
    ~MemoryAnalyzer();

    /* Initialization */
    bool Initialize(const std::string& qemu_host, uint16_t qemu_port = DMA_DEFAULT_PORT);
    bool SetVolatilityPath(const std::string& volatility_path);
    void Shutdown();

    /* Memory operations */
    bool DumpMemoryToFile(const std::string& output_file, uint64_t size = 0x100000000ULL); // 4GB default
    bool AnalyzeMemoryDump(const std::string& memory_file);

    /* Process analysis */
    std::vector<ProcessEntry> GetProcessList() const { return m_processes; }
    std::vector<ProcessEntry> FindProcessByName(const std::string& name) const;
    ProcessEntry* FindProcessByPID(uint32_t pid);
    std::vector<ModuleEntry> GetProcessModules(uint32_t pid);

    /* Memory access */
    bool ReadProcessMemory(uint32_t pid, uint64_t address, void* buffer, size_t size);
    bool WriteProcessMemory(uint32_t pid, uint64_t address, const void* buffer, size_t size);
    uint64_t TranslateProcessVirtualAddress(uint32_t pid, uint64_t virtual_address);

    /* Advanced analysis */
    bool ScanProcessMemory(uint32_t pid, const std::vector<uint8_t>& pattern, 
                          std::vector<uint64_t>& results, uint64_t max_results = 1000);
    bool DumpProcessMemory(uint32_t pid, const std::string& output_dir);

    /* System information */
    std::vector<MemoryRegion> GetMemoryLayout();
    std::string GetSystemInfo() const { return m_system_info; }

    /* Statistics and status */
    bool IsConnected() const;
    std::string GetConnectionStatus() const;
    std::string GetAnalysisStatus() const;
    void PrintStatistics() const;

    /* Error handling */
    std::string GetLastError() const { return m_last_error; }

private:
    /* Internal methods */
    bool ParseVolatilityOutput(const std::string& output);
    bool ParseProcessList(const std::string& pslist_output);
    bool ParseModuleList(const std::string& modules_output, std::vector<ModuleEntry>& modules);
    void SetError(const std::string& error);
    std::string ExecuteVolatilityCommand(const std::string& command, const std::vector<std::string>& args);

    /* Member variables */
    std::unique_ptr<DMAClient> m_dma_client;
    std::unique_ptr<VolatilityInterface> m_volatility;
    
    std::vector<ProcessEntry> m_processes;
    std::map<uint32_t, std::vector<ModuleEntry>> m_process_modules;
    std::vector<MemoryRegion> m_memory_regions;
    
    std::string m_current_memory_file;
    std::string m_system_info;
    std::string m_last_error;
    
    bool m_initialized;
    bool m_memory_analyzed;

    /* Configuration */
    std::string m_qemu_host;
    uint16_t m_qemu_port;
    std::string m_volatility_path;
};

/* Utility functions */
std::string FormatProcessInfo(const ProcessEntry& process);
std::string FormatModuleInfo(const ModuleEntry& module);
bool IsValidProcessName(const std::string& name);
uint64_t ParseHexAddress(const std::string& addr_str);

/*
 * DMA Client Implementation
 * High-performance, thread-safe client for QEMU DMA device
 */

#include "dma_client.h"
#include <iostream>
#include <fstream>
#include <cstring>
#include <algorithm>

#ifdef _WIN32
#include <winsock2.h>
#include <ws2tcpip.h>
#else
#include <unistd.h>
#include <fcntl.h>
#include <sys/select.h>
#include <netdb.h>
#include <errno.h>
#endif

DMAClient::DMAClient(const std::string& host, uint16_t port)
    : m_host(host), m_port(port), m_socket(INVALID_SOCKET), m_connected(false),
      m_winsock_initialized(false), m_bytes_read(0), m_bytes_written(0), m_packets_processed(0) {
    
#ifdef _WIN32
    InitializeWinsock();
#endif
}

DMAClient::~DMAClient() {
    Disconnect();
#ifdef _WIN32
    CleanupWinsock();
#endif
}

bool DMAClient::InitializeWinsock() {
#ifdef _WIN32
    if (m_winsock_initialized) return true;
    
    WSADATA wsaData;
    int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
    if (result != 0) {
        SetError("WSAStartup failed: " + std::to_string(result));
        return false;
    }
    m_winsock_initialized = true;
#endif
    return true;
}

void DMAClient::CleanupWinsock() {
#ifdef _WIN32
    if (m_winsock_initialized) {
        WSACleanup();
        m_winsock_initialized = false;
    }
#endif
}

bool DMAClient::Connect() {
    if (m_connected) {
        return true;
    }

    // Create socket
    m_socket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (m_socket == INVALID_SOCKET) {
        SetError("Failed to create socket");
        return false;
    }

    // Set socket options for performance
#ifdef _WIN32
    DWORD timeout = RECV_TIMEOUT_MS;
    setsockopt(m_socket, SOL_SOCKET, SO_RCVTIMEO, (char*)&timeout, sizeof(timeout));
    timeout = SEND_TIMEOUT_MS;
    setsockopt(m_socket, SOL_SOCKET, SO_SNDTIMEO, (char*)&timeout, sizeof(timeout));
    
    // Disable Nagle's algorithm for low latency
    BOOL nodelay = TRUE;
    setsockopt(m_socket, IPPROTO_TCP, TCP_NODELAY, (char*)&nodelay, sizeof(nodelay));
    
    // Set large buffer sizes for bulk transfers
    int buffer_size = 1024 * 1024; // 1MB
    setsockopt(m_socket, SOL_SOCKET, SO_RCVBUF, (char*)&buffer_size, sizeof(buffer_size));
    setsockopt(m_socket, SOL_SOCKET, SO_SNDBUF, (char*)&buffer_size, sizeof(buffer_size));
#else
    struct timeval timeout;
    timeout.tv_sec = RECV_TIMEOUT_MS / 1000;
    timeout.tv_usec = (RECV_TIMEOUT_MS % 1000) * 1000;
    setsockopt(m_socket, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout));
    timeout.tv_sec = SEND_TIMEOUT_MS / 1000;
    timeout.tv_usec = (SEND_TIMEOUT_MS % 1000) * 1000;
    setsockopt(m_socket, SOL_SOCKET, SO_SNDTIMEO, &timeout, sizeof(timeout));
    
    int nodelay = 1;
    setsockopt(m_socket, IPPROTO_TCP, TCP_NODELAY, &nodelay, sizeof(nodelay));
    
    int buffer_size = 1024 * 1024;
    setsockopt(m_socket, SOL_SOCKET, SO_RCVBUF, &buffer_size, sizeof(buffer_size));
    setsockopt(m_socket, SOL_SOCKET, SO_SNDBUF, &buffer_size, sizeof(buffer_size));
#endif

    // Resolve hostname
    struct sockaddr_in server_addr;
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(m_port);

    if (inet_pton(AF_INET, m_host.c_str(), &server_addr.sin_addr) != 1) {
        // Try to resolve hostname using modern API
        struct addrinfo hints, *result;
        memset(&hints, 0, sizeof(hints));
        hints.ai_family = AF_INET;
        hints.ai_socktype = SOCK_STREAM;

        int ret = getaddrinfo(m_host.c_str(), nullptr, &hints, &result);
        if (ret != 0) {
            SetError("Failed to resolve hostname: " + m_host);
            Disconnect();
            return false;
        }

        struct sockaddr_in* addr_in = (struct sockaddr_in*)result->ai_addr;
        server_addr.sin_addr = addr_in->sin_addr;
        freeaddrinfo(result);
    }

    // Connect with timeout
#ifdef _WIN32
    u_long mode = 1; // Non-blocking mode
    ioctlsocket(m_socket, FIONBIO, &mode);
#else
    int flags = fcntl(m_socket, F_GETFL, 0);
    fcntl(m_socket, F_SETFL, flags | O_NONBLOCK);
#endif

    int result = connect(m_socket, (struct sockaddr*)&server_addr, sizeof(server_addr));
    
#ifdef _WIN32
    if (result == SOCKET_ERROR && WSAGetLastError() != WSAEWOULDBLOCK) {
#else
    if (result < 0 && errno != EINPROGRESS) {
#endif
        SetError("Failed to connect to " + m_host + ":" + std::to_string(m_port));
        Disconnect();
        return false;
    }

    // Wait for connection with timeout
    fd_set write_fds;
    FD_ZERO(&write_fds);
    FD_SET(m_socket, &write_fds);
    
    struct timeval connect_timeout;
    connect_timeout.tv_sec = CONNECT_TIMEOUT_MS / 1000;
    connect_timeout.tv_usec = (CONNECT_TIMEOUT_MS % 1000) * 1000;
    
    result = select(static_cast<int>(m_socket) + 1, nullptr, &write_fds, nullptr, &connect_timeout);
    if (result <= 0) {
        SetError("Connection timeout");
        Disconnect();
        return false;
    }

    // Check for connection errors
    int error = 0;
    socklen_t error_len = sizeof(error);
    getsockopt(m_socket, SOL_SOCKET, SO_ERROR, (char*)&error, &error_len);
    if (error != 0) {
        SetError("Connection failed with error: " + std::to_string(error));
        Disconnect();
        return false;
    }

    // Set back to blocking mode
#ifdef _WIN32
    mode = 0;
    ioctlsocket(m_socket, FIONBIO, &mode);
#else
    flags = fcntl(m_socket, F_GETFL, 0);
    fcntl(m_socket, F_SETFL, flags & ~O_NONBLOCK);
#endif

    m_connected = true;
    m_last_error.clear();
    return true;
}

void DMAClient::Disconnect() {
    if (m_socket != INVALID_SOCKET) {
#ifdef _WIN32
        closesocket(m_socket);
#else
        close(m_socket);
#endif
        m_socket = INVALID_SOCKET;
    }
    m_connected = false;
}

bool DMAClient::SendPacket(const DMAPacket& packet) {
    if (!m_connected) {
        SetError("Not connected");
        return false;
    }

    const char* data = reinterpret_cast<const char*>(&packet);
    size_t total_sent = 0;
    size_t packet_size = sizeof(DMAPacket);

    while (total_sent < packet_size) {
        int sent = send(m_socket, data + total_sent, static_cast<int>(packet_size - total_sent), 0);
        if (sent == SOCKET_ERROR) {
            SetError("Failed to send packet");
            return false;
        }
        total_sent += sent;
    }

    m_packets_processed++;
    return true;
}

bool DMAClient::ReceivePacket(DMAPacket& packet) {
    if (!m_connected) {
        SetError("Not connected");
        return false;
    }

    char* data = reinterpret_cast<char*>(&packet);
    size_t total_received = 0;
    size_t packet_size = sizeof(DMAPacket);

    while (total_received < packet_size) {
        int received = recv(m_socket, data + total_received, static_cast<int>(packet_size - total_received), 0);
        if (received == SOCKET_ERROR || received == 0) {
            SetError("Failed to receive packet");
            return false;
        }
        total_received += received;
    }

    return true;
}

bool DMAClient::SendData(const void* data, size_t size) {
    if (!m_connected) {
        SetError("Not connected");
        return false;
    }

    const char* byte_data = static_cast<const char*>(data);
    size_t total_sent = 0;

    while (total_sent < size) {
        size_t chunk_size = std::min(size - total_sent, static_cast<size_t>(65536)); // 64KB chunks
        int sent = send(m_socket, byte_data + total_sent, static_cast<int>(chunk_size), 0);
        if (sent == SOCKET_ERROR) {
            SetError("Failed to send data");
            return false;
        }
        total_sent += sent;
    }

    m_bytes_written += size;
    return true;
}

bool DMAClient::ReceiveData(void* data, size_t size) {
    if (!m_connected) {
        SetError("Not connected");
        return false;
    }

    char* byte_data = static_cast<char*>(data);
    size_t total_received = 0;

    while (total_received < size) {
        size_t remaining = size - total_received;
        int received = recv(m_socket, byte_data + total_received, static_cast<int>(remaining), 0);
        if (received == SOCKET_ERROR || received == 0) {
            SetError("Failed to receive data");
            return false;
        }
        total_received += received;
    }

    m_bytes_read += size;
    return true;
}

bool DMAClient::SendCommand(const DMAPacket& request, DMAPacket& response) {
    if (!SendPacket(request)) {
        return false;
    }

    if (!ReceivePacket(response)) {
        return false;
    }

    if (!IS_VALID_MAGIC(response.magic)) {
        SetError("Invalid response magic");
        return false;
    }

    return true;
}

bool DMAClient::Ping() {
    DMAPacket request = {};
    request.magic = DMA_MAGIC;
    request.command = CMD_PING;

    DMAPacket response;
    if (!SendCommand(request, response)) {
        return false;
    }

    return response.status == STATUS_SUCCESS;
}

bool DMAClient::ReadPhysicalMemory(uint64_t address, void* buffer, size_t size) {
    if (!IS_VALID_SIZE(size)) {
        SetError("Invalid size");
        return false;
    }

    DMAPacket request = {};
    request.magic = DMA_MAGIC;
    request.command = CMD_READ_PHYS;
    request.param1 = address;
    request.param2 = size;

    DMAPacket response;
    if (!SendCommand(request, response)) {
        return false;
    }

    if (response.status != STATUS_SUCCESS) {
        SetError("Read failed with status: " + std::to_string(response.status));
        return false;
    }

    if (response.data_size != size) {
        SetError("Unexpected data size in response");
        return false;
    }

    return ReceiveData(buffer, size);
}

bool DMAClient::WritePhysicalMemory(uint64_t address, const void* buffer, size_t size) {
    if (!IS_VALID_SIZE(size)) {
        SetError("Invalid size");
        return false;
    }

    DMAPacket request = {};
    request.magic = DMA_MAGIC;
    request.command = CMD_WRITE_PHYS;
    request.param1 = address;
    request.param2 = size;
    request.data_size = static_cast<uint32_t>(size);

    if (!SendPacket(request)) {
        return false;
    }

    if (!SendData(buffer, size)) {
        return false;
    }

    DMAPacket response;
    if (!ReceivePacket(response)) {
        return false;
    }

    if (response.status != STATUS_SUCCESS) {
        SetError("Write failed with status: " + std::to_string(response.status));
        return false;
    }

    return true;
}

uint64_t DMAClient::TranslateVirtualAddress(uint64_t cr3, uint64_t virtual_address) {
    DMAPacket request = {};
    request.magic = DMA_MAGIC;
    request.command = CMD_TRANSLATE_VA;
    request.param1 = virtual_address;
    request.param2 = cr3;

    DMAPacket response;
    if (!SendCommand(request, response)) {
        return 0;
    }

    if (response.status != STATUS_SUCCESS) {
        return 0;
    }

    return response.param1;
}

void DMAClient::SetError(const std::string& error) {
    m_last_error = error;
}

bool DMAClient::DumpMemory(uint64_t start_address, uint64_t size, const std::string& output_file) {
    std::ofstream file(output_file, std::ios::binary);
    if (!file.is_open()) {
        SetError("Failed to open output file: " + output_file);
        return false;
    }

    DMAPacket request = {};
    request.magic = DMA_MAGIC;
    request.command = CMD_DUMP_MEMORY;
    request.param1 = start_address;
    request.param2 = size;

    DMAPacket response;
    if (!SendCommand(request, response)) {
        return false;
    }

    if (response.status != STATUS_SUCCESS) {
        SetError("Memory dump failed with status: " + std::to_string(response.status));
        return false;
    }

    // Receive memory data in chunks
    uint64_t remaining = size;
    std::vector<uint8_t> buffer(DUMP_CHUNK_SIZE);

    while (remaining > 0) {
        size_t chunk_size = static_cast<size_t>(std::min(remaining, static_cast<uint64_t>(DUMP_CHUNK_SIZE)));

        if (!ReceiveData(buffer.data(), chunk_size)) {
            SetError("Failed to receive memory data");
            return false;
        }

        file.write(reinterpret_cast<const char*>(buffer.data()), chunk_size);
        if (file.fail()) {
            SetError("Failed to write to output file");
            return false;
        }

        remaining -= chunk_size;
    }

    file.close();
    return true;
}

bool DMAClient::DumpMemoryToBuffer(uint64_t start_address, uint64_t size, std::vector<uint8_t>& buffer) {
    buffer.resize(static_cast<size_t>(size));

    DMAPacket request = {};
    request.magic = DMA_MAGIC;
    request.command = CMD_DUMP_MEMORY;
    request.param1 = start_address;
    request.param2 = size;

    DMAPacket response;
    if (!SendCommand(request, response)) {
        return false;
    }

    if (response.status != STATUS_SUCCESS) {
        SetError("Memory dump failed with status: " + std::to_string(response.status));
        return false;
    }

    return ReceiveData(buffer.data(), static_cast<size_t>(size));
}

std::vector<MemoryRegion> DMAClient::GetMemoryLayout() {
    std::vector<MemoryRegion> regions;

    DMAPacket request = {};
    request.magic = DMA_MAGIC;
    request.command = CMD_GET_MEMORY_LAYOUT;

    DMAPacket response;
    if (!SendCommand(request, response)) {
        return regions;
    }

    if (response.status != STATUS_SUCCESS) {
        return regions;
    }

    size_t num_regions = response.data_size / sizeof(MemoryRegion);
    if (num_regions > 0) {
        regions.resize(num_regions);
        ReceiveData(regions.data(), response.data_size);
    }

    return regions;
}

void DMAClient::ResetStatistics() {
    m_bytes_read = 0;
    m_bytes_written = 0;
    m_packets_processed = 0;
}

// Utility functions
std::string FormatMemorySize(uint64_t size) {
    const char* units[] = {"B", "KB", "MB", "GB", "TB"};
    int unit_index = 0;
    double formatted_size = static_cast<double>(size);

    while (formatted_size >= 1024.0 && unit_index < 4) {
        formatted_size /= 1024.0;
        unit_index++;
    }

    char buffer[64];
    if (unit_index == 0) {
        sprintf_s(buffer, sizeof(buffer), "%.0f %s", formatted_size, units[unit_index]);
    } else {
        sprintf_s(buffer, sizeof(buffer), "%.2f %s", formatted_size, units[unit_index]);
    }

    return std::string(buffer);
}

std::string FormatAddress(uint64_t address) {
    char buffer[32];
    sprintf_s(buffer, sizeof(buffer), "0x%016llX", static_cast<unsigned long long>(address));
    return std::string(buffer);
}

bool IsValidIPAddress(const std::string& ip) {
    struct sockaddr_in sa;
    return inet_pton(AF_INET, ip.c_str(), &(sa.sin_addr)) == 1;
}

uint16_t ParsePort(const std::string& port_str) {
    try {
        int port = std::stoi(port_str);
        if (port < 1 || port > 65535) {
            return 0;
        }
        return static_cast<uint16_t>(port);
    } catch (...) {
        return 0;
    }
}

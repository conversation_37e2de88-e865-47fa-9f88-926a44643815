#pragma once
/*
 * DMA Client Interface
 * High-performance client for QEMU DMA device communication
 */

#include "dma_protocol.h"
#include <string>
#include <vector>
#include <memory>

#ifdef _WIN32
#include <winsock2.h>
#include <ws2tcpip.h>
#pragma comment(lib, "ws2_32.lib")
typedef SOCKET socket_t;
#else
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
typedef int socket_t;
#define INVALID_SOCKET -1
#define SOCKET_ERROR -1
#endif

class DMAClient {
public:
    DMAClient(const std::string& host = "localhost", uint16_t port = DMA_DEFAULT_PORT);
    ~DMAClient();

    /* Connection management */
    bool Connect();
    void Disconnect();
    bool IsConnected() const { return m_connected; }

    /* Basic operations */
    bool Ping();
    bool ReadPhysicalMemory(uint64_t address, void* buffer, size_t size);
    bool WritePhysicalMemory(uint64_t address, const void* buffer, size_t size);
    uint64_t TranslateVirtualAddress(uint64_t cr3, uint64_t virtual_address);

    /* Bulk operations */
    bool DumpMemory(uint64_t start_address, uint64_t size, const std::string& output_file);
    bool DumpMemoryToBuffer(uint64_t start_address, uint64_t size, std::vector<uint8_t>& buffer);
    std::vector<MemoryRegion> GetMemoryLayout();

    /* Statistics */
    uint64_t GetBytesRead() const { return m_bytes_read; }
    uint64_t GetBytesWritten() const { return m_bytes_written; }
    uint32_t GetPacketsProcessed() const { return m_packets_processed; }
    void ResetStatistics();

    /* Error handling */
    std::string GetLastError() const { return m_last_error; }

private:
    /* Internal methods */
    bool SendPacket(const DMAPacket& packet);
    bool ReceivePacket(DMAPacket& packet);
    bool SendData(const void* data, size_t size);
    bool ReceiveData(void* data, size_t size);
    bool SendCommand(const DMAPacket& request, DMAPacket& response);
    bool SendCommandWithData(const DMAPacket& request, const void* send_data, 
                           DMAPacket& response, void* recv_data = nullptr, size_t recv_size = 0);
    
    void SetError(const std::string& error);
    bool InitializeWinsock();
    void CleanupWinsock();

    /* Member variables */
    std::string m_host;
    uint16_t m_port;
    socket_t m_socket;
    bool m_connected;
    bool m_winsock_initialized;

    /* Statistics */
    uint64_t m_bytes_read;
    uint64_t m_bytes_written;
    uint32_t m_packets_processed;

    /* Error handling */
    std::string m_last_error;

    /* Constants */
    static const int CONNECT_TIMEOUT_MS = 5000;
    static const int RECV_TIMEOUT_MS = 30000;
    static const int SEND_TIMEOUT_MS = 30000;
};

/* Utility functions */
std::string FormatMemorySize(uint64_t size);
std::string FormatAddress(uint64_t address);
bool IsValidIPAddress(const std::string& ip);
uint16_t ParsePort(const std::string& port_str);

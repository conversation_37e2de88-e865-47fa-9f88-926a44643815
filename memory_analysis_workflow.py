#!/usr/bin/env python3
"""
Complete Memory Analysis Workflow
Integrates QEMU DMA device, memory dumping, and Volatility 3 analysis
"""

import json
import time
import argparse
from pathlib import Path
from dma_memory_analyzer import DMAClient, VolatilityAnalyzer, parse_size

class ProcessInfo:
    """Process information extracted from Volatility output"""
    def __init__(self, pid, ppid, name, offset, threads, handles, session_id, wow64, create_time):
        self.pid = pid
        self.ppid = ppid
        self.name = name
        self.offset = offset
        self.threads = threads
        self.handles = handles
        self.session_id = session_id
        self.wow64 = wow64
        self.create_time = create_time
    
    def to_dict(self):
        return {
            'pid': self.pid,
            'ppid': self.ppid,
            'name': self.name,
            'offset': self.offset,
            'threads': self.threads,
            'handles': self.handles,
            'session_id': self.session_id,
            'wow64': self.wow64,
            'create_time': self.create_time
        }

class MemoryAnalysisWorkflow:
    """Complete workflow for memory analysis"""
    
    def __init__(self, qemu_host='localhost', qemu_port=31338, volatility_path='volatility3/vol.py'):
        self.qemu_host = qemu_host
        self.qemu_port = qemu_port
        self.volatility_path = volatility_path
        self.dma_client = None
        self.analyzer = None
        self.processes = []
        self.memory_file = None
    
    def connect_to_qemu(self):
        """Connect to QEMU DMA device"""
        print(f"Connecting to QEMU DMA device at {self.qemu_host}:{self.qemu_port}")
        self.dma_client = DMAClient(self.qemu_host, self.qemu_port)
        
        if not self.dma_client.connect():
            print("Failed to connect to QEMU DMA device")
            return False
        
        if not self.dma_client.ping():
            print("DMA device ping failed")
            return False
        
        print("Successfully connected to QEMU DMA device")
        return True
    
    def dump_memory(self, output_file, start_addr=0x0, size='4GB'):
        """Dump physical memory from QEMU"""
        print(f"Dumping memory to {output_file}")
        
        if not self.dma_client:
            print("Not connected to DMA device")
            return False
        
        # Get memory layout
        regions = self.dma_client.get_memory_layout()
        print("Available memory regions:")
        for region in regions:
            print(f"  0x{region['start']:016x} - 0x{region['end']:016x} "
                  f"({region['size']//1024//1024}MB)")
        
        # Dump memory
        dump_size = parse_size(size)
        if self.dma_client.dump_memory(start_addr, dump_size, output_file):
            self.memory_file = output_file
            print(f"Memory dump completed: {output_file}")
            return True
        else:
            print("Memory dump failed")
            return False
    
    def analyze_memory(self, memory_file=None):
        """Analyze memory dump with Volatility 3"""
        if memory_file:
            self.memory_file = memory_file
        
        if not self.memory_file:
            print("No memory file specified")
            return False
        
        print(f"Analyzing memory dump: {self.memory_file}")
        self.analyzer = VolatilityAnalyzer(self.volatility_path)
        
        # Get system information
        print("Getting system information...")
        info_result = self.analyzer.run_plugin(self.memory_file, 'windows.info')
        if info_result:
            print("System Information:")
            print(info_result)
        
        # Get process list
        print("Extracting process list...")
        pslist_result = self.analyzer.list_processes(self.memory_file)
        if pslist_result:
            self.processes = self.parse_process_list(pslist_result)
            print(f"Found {len(self.processes)} processes")
        
        return True
    
    def parse_process_list(self, pslist_output):
        """Parse Volatility pslist output into ProcessInfo objects"""
        processes = []
        lines = pslist_output.strip().split('\n')
        
        # Skip header lines
        data_start = False
        for line in lines:
            if 'PID' in line and 'PPID' in line and 'ImageFileName' in line:
                data_start = True
                continue
            
            if not data_start or not line.strip():
                continue
            
            # Parse process line
            parts = line.split()
            if len(parts) >= 8:
                try:
                    pid = int(parts[0])
                    ppid = int(parts[1])
                    name = parts[2]
                    offset = parts[3]
                    threads = int(parts[4])
                    handles = int(parts[5])
                    session_id = parts[6]
                    wow64 = parts[7] == 'True'
                    create_time = ' '.join(parts[8:10]) if len(parts) >= 10 else 'N/A'
                    
                    process = ProcessInfo(pid, ppid, name, offset, threads, handles, 
                                        session_id, wow64, create_time)
                    processes.append(process)
                except (ValueError, IndexError):
                    continue
        
        return processes
    
    def find_process_by_name(self, name):
        """Find processes by name"""
        matches = []
        for process in self.processes:
            if name.lower() in process.name.lower():
                matches.append(process)
        return matches
    
    def find_process_by_pid(self, pid):
        """Find process by PID"""
        for process in self.processes:
            if process.pid == pid:
                return process
        return None
    
    def get_process_memory_address(self, pid):
        """Get process memory address for direct memory access"""
        process = self.find_process_by_pid(pid)
        if process:
            # Extract address from offset (format: 0xfa8000cbc040)
            try:
                if process.offset.startswith('0x'):
                    return int(process.offset, 16)
            except ValueError:
                pass
        return None
    
    def read_process_memory(self, pid, address, size):
        """Read memory from a specific process"""
        if not self.dma_client:
            print("Not connected to DMA device")
            return None
        
        # For now, read physical memory directly
        # In a full implementation, you'd need CR3 for virtual address translation
        return self.dma_client.read_physical_memory(address, size)
    
    def write_process_memory(self, pid, address, data):
        """Write memory to a specific process"""
        if not self.dma_client:
            print("Not connected to DMA device")
            return False
        
        # Create write packet
        from dma_memory_analyzer import DMAPacket, CMD_WRITE_PHYS
        packet = DMAPacket(CMD_WRITE_PHYS, address, len(data))
        packet.data_size = len(data)
        
        # Send command with data
        try:
            self.dma_client.sock.sendall(packet.pack())
            self.dma_client.sock.sendall(data)
            
            # Receive response
            response_data = self.dma_client.sock.recv(48)
            response = DMAPacket.unpack(response_data)
            
            return response.status == 0
        except Exception as e:
            print(f"Write failed: {e}")
            return False
    
    def save_analysis_report(self, output_file):
        """Save analysis report to JSON file"""
        report = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'memory_file': self.memory_file,
            'qemu_host': self.qemu_host,
            'qemu_port': self.qemu_port,
            'process_count': len(self.processes),
            'processes': [p.to_dict() for p in self.processes]
        }
        
        with open(output_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"Analysis report saved to {output_file}")
    
    def interactive_mode(self):
        """Interactive mode for process exploration"""
        print("\n=== Interactive Memory Analysis Mode ===")
        print("Commands:")
        print("  list - List all processes")
        print("  find <name> - Find processes by name")
        print("  info <pid> - Get process information")
        print("  read <pid> <addr> <size> - Read process memory")
        print("  write <pid> <addr> <data> - Write process memory (hex)")
        print("  quit - Exit interactive mode")
        
        while True:
            try:
                cmd = input("\n> ").strip().split()
                if not cmd:
                    continue
                
                if cmd[0] == 'quit':
                    break
                elif cmd[0] == 'list':
                    for p in self.processes[:20]:  # Show first 20
                        print(f"PID {p.pid:5d}: {p.name} (PPID: {p.ppid})")
                    if len(self.processes) > 20:
                        print(f"... and {len(self.processes) - 20} more processes")
                
                elif cmd[0] == 'find' and len(cmd) > 1:
                    matches = self.find_process_by_name(cmd[1])
                    for p in matches:
                        print(f"PID {p.pid:5d}: {p.name} (PPID: {p.ppid}, Offset: {p.offset})")
                
                elif cmd[0] == 'info' and len(cmd) > 1:
                    pid = int(cmd[1])
                    process = self.find_process_by_pid(pid)
                    if process:
                        print(f"Process Information:")
                        print(f"  PID: {process.pid}")
                        print(f"  Name: {process.name}")
                        print(f"  PPID: {process.ppid}")
                        print(f"  Offset: {process.offset}")
                        print(f"  Threads: {process.threads}")
                        print(f"  Handles: {process.handles}")
                        print(f"  Session ID: {process.session_id}")
                        print(f"  WoW64: {process.wow64}")
                        print(f"  Create Time: {process.create_time}")
                    else:
                        print(f"Process with PID {pid} not found")
                
                elif cmd[0] == 'read' and len(cmd) >= 4:
                    pid = int(cmd[1])
                    addr = int(cmd[2], 0)
                    size = int(cmd[3])
                    data = self.read_process_memory(pid, addr, size)
                    if data:
                        print(f"Read {len(data)} bytes from 0x{addr:x}:")
                        print(data.hex())
                    else:
                        print("Read failed")
                
                elif cmd[0] == 'write' and len(cmd) >= 4:
                    pid = int(cmd[1])
                    addr = int(cmd[2], 0)
                    data = bytes.fromhex(cmd[3])
                    if self.write_process_memory(pid, addr, data):
                        print(f"Wrote {len(data)} bytes to 0x{addr:x}")
                    else:
                        print("Write failed")
                
                else:
                    print("Unknown command or invalid arguments")
            
            except (ValueError, IndexError) as e:
                print(f"Invalid command: {e}")
            except KeyboardInterrupt:
                break
    
    def cleanup(self):
        """Cleanup resources"""
        if self.dma_client:
            self.dma_client.disconnect()

def main():
    parser = argparse.ArgumentParser(description='Memory Analysis Workflow')
    parser.add_argument('--host', default='localhost', help='QEMU host address')
    parser.add_argument('--port', type=int, default=31338, help='DMA device port')
    parser.add_argument('--volatility-path', default='volatility3/vol.py', help='Path to Volatility 3')
    parser.add_argument('--dump-size', default='4GB', help='Memory dump size')
    parser.add_argument('--output', default='memory.raw', help='Memory dump output file')
    parser.add_argument('--analyze-only', help='Analyze existing memory dump file')
    parser.add_argument('--report', default='analysis_report.json', help='Analysis report output file')
    parser.add_argument('--interactive', action='store_true', help='Enter interactive mode')
    
    args = parser.parse_args()
    
    workflow = MemoryAnalysisWorkflow(args.host, args.port, args.volatility_path)
    
    try:
        if args.analyze_only:
            # Analyze existing memory dump
            if workflow.analyze_memory(args.analyze_only):
                workflow.save_analysis_report(args.report)
                if args.interactive:
                    workflow.interactive_mode()
        else:
            # Full workflow: connect, dump, analyze
            if workflow.connect_to_qemu():
                if workflow.dump_memory(args.output, size=args.dump_size):
                    if workflow.analyze_memory():
                        workflow.save_analysis_report(args.report)
                        if args.interactive:
                            workflow.interactive_mode()
    
    except KeyboardInterrupt:
        print("\nInterrupted by user")
    finally:
        workflow.cleanup()

if __name__ == '__main__':
    main()

#!/bin/bash
# Deployment commands for Streamlined DMA device
# Run these commands on your Proxmox server

echo "=== Streamlined DMA Device Deployment ==="
echo "Current directory: $(pwd)"
echo "QEMU version: $(qemu-system-x86_64 --version | head -1)"

# Check if we're in the right directory
if [ ! -d "hw" ]; then
    echo "Error: Not in QEMU source directory. Please cd to your QEMU source first."
    exit 1
fi

# Backup original files
echo "Creating backups..."
if [ -f "hw/misc/meson.build" ]; then
    cp hw/misc/meson.build hw/misc/meson.build.backup
    echo "Backed up hw/misc/meson.build"
fi

# Check if streamlined_dma.c exists
if [ ! -f "hw/misc/streamlined_dma.c" ]; then
    echo "Error: streamlined_dma.c not found in hw/misc/"
    echo "Please copy the file first:"
    echo "scp /path/to/streamlined_dma.c user@proxmox:$(pwd)/hw/misc/"
    exit 1
fi

echo "Found streamlined_dma.c"

# Update meson.build
echo "Updating hw/misc/meson.build..."
if ! grep -q "streamlined_dma.c" hw/misc/meson.build; then
    # Find a good place to add our device
    if grep -q "misc_ss.add.*\.c" hw/misc/meson.build; then
        # Add after existing misc_ss.add lines
        sed -i '/misc_ss\.add.*\.c/a misc_ss.add(files('\''streamlined_dma.c'\''))' hw/misc/meson.build
    else
        # Add at the end
        echo "misc_ss.add(files('streamlined_dma.c'))" >> hw/misc/meson.build
    fi
    echo "Added streamlined_dma.c to meson.build"
else
    echo "streamlined_dma.c already in meson.build"
fi

# Show what was added
echo "Current meson.build content:"
grep -n "streamlined_dma" hw/misc/meson.build || echo "Not found in meson.build"

# Build QEMU
echo "Building QEMU..."
cd build || { echo "Error: build directory not found"; exit 1; }

echo "Running make clean..."
make clean

echo "Running make with $(nproc) parallel jobs..."
if make -j$(nproc); then
    echo "✓ QEMU build successful"
else
    echo "✗ QEMU build failed"
    echo "Check the error messages above"
    exit 1
fi

# Install QEMU
echo "Installing QEMU..."
if make install; then
    echo "✓ QEMU installation successful"
else
    echo "✗ QEMU installation failed"
    exit 1
fi

echo "=== Deployment completed successfully! ==="
echo ""
echo "Next steps:"
echo "1. Stop VM 102: qm stop 102"
echo "2. Edit VM config: nano /etc/pve/qemu-server/102.conf"
echo "3. Add this line: args: -device streamlined-dma,id=dma0"
echo "4. Start VM 102: qm start 102"
echo "5. Test connection from external host on port 31338"
echo ""
echo "VM Configuration example:"
echo "args: -device streamlined-dma,id=dma0"
echo ""
echo "Test connection:"
echo "python3 dma_memory_analyzer.py --host <proxmox_ip> --port 31338"

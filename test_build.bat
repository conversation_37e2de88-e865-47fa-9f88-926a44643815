@echo off
REM Quick test build for DMA Memory Analyzer

echo Testing compilation...

REM Check if MSBuild is available
where msbuild >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Error: MSBuild not found. Please run from Visual Studio Developer Command Prompt.
    echo.
    echo To fix this:
    echo 1. Open "Developer Command Prompt for VS 2019"
    echo 2. Navigate to this directory
    echo 3. Run this script again
    pause
    exit /b 1
)

REM Create directories if they don't exist
if not exist "bin" mkdir bin
if not exist "bin\x64" mkdir bin\x64
if not exist "bin\x64\Debug" mkdir bin\x64\Debug
if not exist "obj" mkdir obj

echo Building Debug configuration (test)...
msbuild DMAMemoryAnalyzer.sln /p:Configuration=Debug /p:Platform=x64 /verbosity:minimal

if %ERRORLEVEL% neq 0 (
    echo.
    echo *** BUILD FAILED ***
    echo.
    echo Common issues and solutions:
    echo 1. Missing Visual Studio 2019 - Install Visual Studio 2019 with C++ workload
    echo 2. Wrong command prompt - Use "Developer Command Prompt for VS 2019"
    echo 3. Missing Windows SDK - Install Windows 10 SDK
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo *** BUILD SUCCESSFUL ***
    echo.
    echo Executable created: bin\x64\Debug\DMAMemoryAnalyzer.exe
    echo.
    echo Test the executable:
    echo   bin\x64\Debug\DMAMemoryAnalyzer.exe --help
    echo.
    echo To build Release version:
    echo   build.bat
    echo.
)

pause

# Streamlined DMA Memory Analysis System

A complete solution for analyzing Windows VM memory using QEMU DMA device and Volatility 3.

## Overview

This project provides:
- **Streamlined DMA Device**: A QEMU PCI device for direct physical memory access
- **Memory Analysis Client**: Python client for memory dumping and analysis
- **Volatility 3 Integration**: Automated process enumeration and memory forensics
- **Complete Workflow**: End-to-end memory analysis pipeline

## Architecture

```
Windows VM (QEMU) <---> Streamlined DMA Device <---> Network <---> Analysis Client <---> Volatility 3
```

## Features

### QEMU DMA Device
- Physical memory read/write access
- Bulk memory dumping (up to 4GB+)
- Virtual address translation
- Network API (TCP port 31338)
- Memory layout detection

### Analysis Client
- Connect to remote QEMU DMA device
- Dump complete physical memory
- Integrate with Volatility 3
- Process enumeration and analysis
- Interactive memory exploration

### Volatility 3 Integration
- Automated process list extraction
- Process tree visualization
- Memory forensics analysis
- Export process information

## Quick Start

### 1. Setup Volatility 3
```bash
python3 setup_volatility3.py
```

### 2. Deploy DMA Device to QEMU
```bash
# Copy streamlined DMA device to QEMU source
python3 deploy_to_qemu.py /path/to/qemu/source

# Or manually:
scp example_qemu/hw/misc/streamlined_dma.c user@proxmox:/path/to/qemu/hw/misc/
```

### 3. Build and Install QEMU
```bash
# On Proxmox server
cd /path/to/qemu/source
# Edit hw/misc/meson.build to add: misc_ss.add(files('streamlined_dma.c'))
cd build
make -j$(nproc)
make install
```

### 4. Configure VM
Add to VM configuration:
```
args: -device streamlined-dma,id=dma0
```

### 5. Start Analysis
```bash
# Full workflow: connect, dump, analyze
python3 memory_analysis_workflow.py --host <proxmox_ip> --dump-size 4GB

# Analyze existing dump
python3 memory_analysis_workflow.py --analyze-only memory.raw --interactive
```

## Usage Examples

### Basic Memory Dump
```bash
python3 dma_memory_analyzer.py --host ************* --dump-size 4GB --output vm_memory.raw
```

### Process Analysis
```bash
python3 dma_memory_analyzer.py --analyze vm_memory.raw --list-processes
```

### Interactive Mode
```bash
python3 memory_analysis_workflow.py --analyze-only vm_memory.raw --interactive
```

Interactive commands:
- `list` - List all processes
- `find notepad` - Find processes by name
- `info 1234` - Get process information
- `read 1234 0x400000 256` - Read process memory
- `write 1234 0x400000 deadbeef` - Write process memory

## File Structure

```
├── example_qemu/hw/misc/
│   ├── streamlined_dma.c          # QEMU DMA device implementation
│   └── advdma_nic.c              # Original complex device (reference)
├── dma_memory_analyzer.py         # Memory dump and analysis client
├── memory_analysis_workflow.py    # Complete workflow integration
├── setup_volatility3.py          # Volatility 3 setup script
├── deploy_to_qemu.py             # QEMU deployment script
└── README.md                     # This file
```

## Protocol Specification

### DMA Packet Format
```c
typedef struct {
    uint32_t magic;      // 0x444D4100 ('DMA\0')
    uint32_t command;    // Command code
    uint32_t status;     // Response status
    uint32_t data_size;  // Data payload size
    uint64_t param1;     // Parameter 1
    uint64_t param2;     // Parameter 2
    uint64_t param3;     // Parameter 3
    uint32_t reserved[4]; // Reserved fields
} DMAPacket;
```

### Commands
- `CMD_PING (0x1000)` - Test connection
- `CMD_READ_PHYS (0x2000)` - Read physical memory
- `CMD_WRITE_PHYS (0x2001)` - Write physical memory
- `CMD_TRANSLATE_VA (0x2002)` - Translate virtual address
- `CMD_DUMP_MEMORY (0x3000)` - Bulk memory dump
- `CMD_GET_MEMORY_LAYOUT (0x3001)` - Get memory regions

## Security Considerations

⚠️ **WARNING**: This tool provides unrestricted memory access to the target VM.

- Use only in controlled environments
- Ensure network security between analysis host and QEMU
- Consider firewall rules to restrict access to port 31338
- Monitor for unauthorized access attempts

## Troubleshooting

### Connection Issues
```bash
# Test DMA device connectivity
python3 -c "
from dma_memory_analyzer import DMAClient
client = DMAClient('*************', 31338)
if client.connect():
    print('Connected successfully')
    client.ping()
else:
    print('Connection failed')
"
```

### QEMU Build Issues
```bash
# Check if device is properly integrated
grep -r "streamlined-dma" /path/to/qemu/source/
```

### Volatility Issues
```bash
# Test Volatility 3 installation
python3 volatility3/vol.py -h
```

## Advanced Usage

### Custom Memory Regions
```python
from dma_memory_analyzer import DMAClient

client = DMAClient('*************')
client.connect()

# Get memory layout
regions = client.get_memory_layout()
for region in regions:
    print(f"Region: 0x{region['start']:x} - 0x{region['end']:x}")
```

### Process Memory Manipulation
```python
from memory_analysis_workflow import MemoryAnalysisWorkflow

workflow = MemoryAnalysisWorkflow('*************')
workflow.connect_to_qemu()
workflow.analyze_memory('memory.raw')

# Find notepad process
processes = workflow.find_process_by_name('notepad')
if processes:
    pid = processes[0].pid
    # Read process memory
    data = workflow.read_process_memory(pid, 0x400000, 1024)
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is provided for educational and research purposes. Use responsibly and in accordance with applicable laws and regulations.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review QEMU and Volatility documentation
3. Create an issue with detailed information about your setup

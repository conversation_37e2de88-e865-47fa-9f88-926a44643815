/*
 * Physical memory access helper for QEMU
 * This function provides a unified interface for both reading and writing
 * physical memory, similar to the deprecated cpu_physical_memory_read/write functions.
 */

#include "qemu/osdep.h"
#include "exec/cpu-all.h"
#include "qemu/cutils.h"
#include "exec/memory.h"
#include "exec/address-spaces.h"

/*
 * cpu_physical_memory_rw - Access physical memory
 * @addr: Physical memory address to access
 * @buf: Buffer to read into or write from
 * @len: Length of the buffer
 * @is_write: 0 for read, 1 for write
 *
 * Returns 0 on success, non-zero on error
 */
int cpu_physical_memory_rw(hwaddr addr, uint8_t *buf, hwaddr len, int is_write)
{
    if (is_write) {
        address_space_write(&address_space_memory, addr, MEMTXATTRS_UNSPECIFIED, 
                           buf, len);
    } else {
        address_space_read(&address_space_memory, addr, MEMTXATTRS_UNSPECIFIED, 
                          buf, len);
    }
    return 0;
}

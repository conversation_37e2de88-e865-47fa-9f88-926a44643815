# PowerShell script to deploy Streamlined DMA device to Proxmox
# Update these variables with your Proxmox details

param(
    [string]$ProxmoxHost = "",
    [string]$ProxmoxUser = "root",
    [string]$QemuSourcePath = "/usr/src/qemu",
    [switch]$Help
)

if ($Help) {
    Write-Host "Usage: .\deploy_to_proxmox.ps1 -ProxmoxHost <ip> [-ProxmoxUser <user>] [-QemuSourcePath <path>]"
    Write-Host ""
    Write-Host "Parameters:"
    Write-Host "  -ProxmoxHost      IP address of Proxmox server (required)"
    Write-Host "  -ProxmoxUser      SSH username (default: root)"
    Write-Host "  -QemuSourcePath   Path to QEMU source on Proxmox (default: /usr/src/qemu)"
    Write-Host ""
    Write-Host "Example:"
    Write-Host "  .\deploy_to_proxmox.ps1 -ProxmoxHost *************"
    exit 0
}

if (-not $ProxmoxHost) {
    Write-Host "Error: ProxmoxHost is required"
    Write-Host "Use -Help for usage information"
    exit 1
}

Write-Host "=== Deploying Streamlined DMA Device to Proxmox ===" -ForegroundColor Green
Write-Host ""
Write-Host "Configuration:"
Write-Host "  Proxmox Host: $ProxmoxHost"
Write-Host "  SSH User: $ProxmoxUser"
Write-Host "  QEMU Source Path: $QemuSourcePath"
Write-Host ""

# Check if required files exist
$sourceFile = "example_qemu\hw\misc\streamlined_dma.c"
$deployScript = "deploy_commands.sh"

if (-not (Test-Path $sourceFile)) {
    Write-Host "Error: $sourceFile not found" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path $deployScript)) {
    Write-Host "Error: $deployScript not found" -ForegroundColor Red
    exit 1
}

Write-Host "Step 1: Copying streamlined_dma.c to Proxmox..." -ForegroundColor Yellow
$scpCmd1 = "scp `"$sourceFile`" ${ProxmoxUser}@${ProxmoxHost}:${QemuSourcePath}/hw/misc/"
Write-Host "Running: $scpCmd1"
$result1 = Invoke-Expression $scpCmd1
if ($LASTEXITCODE -ne 0) {
    Write-Host "Error: Failed to copy streamlined_dma.c" -ForegroundColor Red
    exit 1
}

Write-Host "Step 2: Copying deployment script to Proxmox..." -ForegroundColor Yellow
$scpCmd2 = "scp `"$deployScript`" ${ProxmoxUser}@${ProxmoxHost}:${QemuSourcePath}/"
Write-Host "Running: $scpCmd2"
$result2 = Invoke-Expression $scpCmd2
if ($LASTEXITCODE -ne 0) {
    Write-Host "Error: Failed to copy deploy_commands.sh" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Files copied successfully!" -ForegroundColor Green
Write-Host ""
Write-Host "Step 3: Running deployment on Proxmox..." -ForegroundColor Yellow
$sshCmd = "ssh ${ProxmoxUser}@${ProxmoxHost} `"cd ${QemuSourcePath} && chmod +x deploy_commands.sh && ./deploy_commands.sh`""
Write-Host "Running: $sshCmd"
Write-Host ""

# Execute the deployment
Invoke-Expression $sshCmd

if ($LASTEXITCODE -eq 0) {
    Write-Host ""
    Write-Host "=== Deployment completed successfully! ===" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Yellow
    Write-Host "1. Stop VM 102: ssh ${ProxmoxUser}@${ProxmoxHost} 'qm stop 102'"
    Write-Host "2. Edit VM config: ssh ${ProxmoxUser}@${ProxmoxHost} 'nano /etc/pve/qemu-server/102.conf'"
    Write-Host "3. Add this line to the config file:"
    Write-Host "   args: -device streamlined-dma,id=dma0" -ForegroundColor Cyan
    Write-Host "4. Start VM 102: ssh ${ProxmoxUser}@${ProxmoxHost} 'qm start 102'"
    Write-Host "5. Test the connection:"
    Write-Host "   python3 dma_memory_analyzer.py --host $ProxmoxHost --port 31338" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "VM Configuration example:" -ForegroundColor Yellow
    Write-Host "Add to /etc/pve/qemu-server/102.conf:"
    Write-Host "args: -device streamlined-dma,id=dma0" -ForegroundColor Cyan
} else {
    Write-Host ""
    Write-Host "Deployment failed. Check the error messages above." -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

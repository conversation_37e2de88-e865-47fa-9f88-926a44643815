#!/usr/bin/env python3
"""
DMA Memory Analyzer
Connects to QEMU DMA device, dumps memory, and analyzes with Volatility 3

Usage:
    python dma_memory_analyzer.py --host <qemu_host> --port 31338 --dump-size 4GB --output memory.raw
    python dma_memory_analyzer.py --analyze memory.raw --volatility-path /path/to/vol.py
"""

import socket
import struct
import argparse
import os
import sys
import subprocess
import tempfile
from pathlib import Path

# DMA Protocol Constants
DMA_MAGIC = 0x444D4100  # 'DMA\0'
CMD_PING = 0x1000
CMD_READ_PHYS = 0x2000
CMD_WRITE_PHYS = 0x2001
CMD_TRANSLATE_VA = 0x2002
CMD_DUMP_MEMORY = 0x3000
CMD_GET_MEMORY_LAYOUT = 0x3001

class DMAPacket:
    """DMA Protocol packet structure"""
    def __init__(self, command=0, param1=0, param2=0, param3=0, data_size=0):
        self.magic = DMA_MAGIC
        self.command = command
        self.status = 0
        self.data_size = data_size
        self.param1 = param1
        self.param2 = param2
        self.param3 = param3
        self.reserved = [0, 0, 0, 0]
    
    def pack(self):
        """Pack packet into binary format"""
        return struct.pack('<IIIIQQQIIII', 
                          self.magic, self.command, self.status, self.data_size,
                          self.param1, self.param2, self.param3,
                          *self.reserved)
    
    @classmethod
    def unpack(cls, data):
        """Unpack binary data into packet"""
        unpacked = struct.unpack('<IIIIQQQIIII', data)
        packet = cls()
        packet.magic = unpacked[0]
        packet.command = unpacked[1]
        packet.status = unpacked[2]
        packet.data_size = unpacked[3]
        packet.param1 = unpacked[4]
        packet.param2 = unpacked[5]
        packet.param3 = unpacked[6]
        packet.reserved = list(unpacked[7:11])
        return packet

class DMAClient:
    """Client for connecting to QEMU DMA device"""
    
    def __init__(self, host='localhost', port=31338):
        self.host = host
        self.port = port
        self.sock = None
    
    def connect(self):
        """Connect to DMA device"""
        try:
            self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.sock.connect((self.host, self.port))
            print(f"Connected to DMA device at {self.host}:{self.port}")
            return True
        except Exception as e:
            print(f"Failed to connect: {e}")
            return False
    
    def disconnect(self):
        """Disconnect from DMA device"""
        if self.sock:
            self.sock.close()
            self.sock = None
    
    def send_command(self, packet):
        """Send command packet and receive response"""
        try:
            # Send packet
            self.sock.sendall(packet.pack())
            
            # Receive response header
            response_data = self.sock.recv(48)  # Packet size
            if len(response_data) != 48:
                raise Exception("Invalid response size")
            
            response = DMAPacket.unpack(response_data)
            
            # Receive additional data if present
            data = b''
            if response.data_size > 0:
                remaining = response.data_size
                while remaining > 0:
                    chunk = self.sock.recv(min(remaining, 65536))
                    if not chunk:
                        break
                    data += chunk
                    remaining -= len(chunk)
            
            return response, data
        except Exception as e:
            print(f"Command failed: {e}")
            return None, None
    
    def ping(self):
        """Test connection with ping"""
        packet = DMAPacket(CMD_PING)
        response, _ = self.send_command(packet)
        if response and response.status == 0:
            print("Ping successful!")
            return True
        else:
            print("Ping failed!")
            return False
    
    def read_physical_memory(self, address, size):
        """Read physical memory"""
        packet = DMAPacket(CMD_READ_PHYS, address, size)
        response, data = self.send_command(packet)
        if response and response.status == 0:
            return data
        return None
    
    def dump_memory(self, start_addr, size, output_file):
        """Dump memory to file"""
        print(f"Dumping {size} bytes from 0x{start_addr:016x} to {output_file}")
        
        packet = DMAPacket(CMD_DUMP_MEMORY, start_addr, size)
        response, data = self.send_command(packet)
        
        if response and response.status == 0:
            with open(output_file, 'wb') as f:
                f.write(data)
            print(f"Memory dump completed: {len(data)} bytes written")
            return True
        else:
            print("Memory dump failed")
            return False
    
    def get_memory_layout(self):
        """Get memory layout information"""
        packet = DMAPacket(CMD_GET_MEMORY_LAYOUT)
        response, data = self.send_command(packet)
        
        if response and response.status == 0:
            regions = []
            for i in range(0, len(data), 16):  # Each region is 16 bytes
                if i + 16 <= len(data):
                    start, size, flags, reserved = struct.unpack('<QQII', data[i:i+16])
                    regions.append({
                        'start': start,
                        'size': size,
                        'flags': flags,
                        'end': start + size
                    })
            return regions
        return []

class VolatilityAnalyzer:
    """Wrapper for Volatility 3 analysis"""
    
    def __init__(self, volatility_path='vol.py'):
        self.volatility_path = volatility_path
    
    def run_plugin(self, memory_file, plugin, extra_args=None):
        """Run a Volatility 3 plugin"""
        cmd = ['python3', self.volatility_path, '-f', memory_file, plugin]
        if extra_args:
            cmd.extend(extra_args)
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            if result.returncode == 0:
                return result.stdout
            else:
                print(f"Volatility error: {result.stderr}")
                return None
        except subprocess.TimeoutExpired:
            print("Volatility command timed out")
            return None
        except Exception as e:
            print(f"Failed to run Volatility: {e}")
            return None
    
    def list_processes(self, memory_file):
        """List processes using windows.pslist"""
        print("Analyzing processes with Volatility 3...")
        return self.run_plugin(memory_file, 'windows.pslist')
    
    def process_tree(self, memory_file):
        """Show process tree using windows.pstree"""
        print("Generating process tree...")
        return self.run_plugin(memory_file, 'windows.pstree')
    
    def dump_process_memory(self, memory_file, pid, output_dir):
        """Dump process memory using windows.memmap"""
        print(f"Dumping memory for PID {pid}...")
        return self.run_plugin(memory_file, 'windows.memmap', 
                             ['--pid', str(pid), '--dump', '--output-dir', output_dir])

def parse_size(size_str):
    """Parse size string (e.g., '4GB', '1024MB', '512KB')"""
    size_str = size_str.upper()
    if size_str.endswith('GB'):
        return int(float(size_str[:-2]) * 1024 * 1024 * 1024)
    elif size_str.endswith('MB'):
        return int(float(size_str[:-2]) * 1024 * 1024)
    elif size_str.endswith('KB'):
        return int(float(size_str[:-2]) * 1024)
    else:
        return int(size_str)

def main():
    parser = argparse.ArgumentParser(description='DMA Memory Analyzer')
    parser.add_argument('--host', default='localhost', help='QEMU host address')
    parser.add_argument('--port', type=int, default=31338, help='DMA device port')
    parser.add_argument('--dump-size', default='4GB', help='Memory dump size (e.g., 4GB, 1024MB)')
    parser.add_argument('--start-addr', default='0x0', help='Start address for memory dump')
    parser.add_argument('--output', default='memory.raw', help='Output file for memory dump')
    parser.add_argument('--analyze', help='Analyze existing memory dump file')
    parser.add_argument('--volatility-path', default='vol.py', help='Path to Volatility 3 vol.py')
    parser.add_argument('--list-processes', action='store_true', help='List processes only')
    parser.add_argument('--process-tree', action='store_true', help='Show process tree only')
    
    args = parser.parse_args()
    
    if args.analyze:
        # Analyze existing memory dump
        analyzer = VolatilityAnalyzer(args.volatility_path)
        
        if args.list_processes:
            result = analyzer.list_processes(args.analyze)
            if result:
                print(result)
        elif args.process_tree:
            result = analyzer.process_tree(args.analyze)
            if result:
                print(result)
        else:
            # Full analysis
            print("=== Process List ===")
            result = analyzer.list_processes(args.analyze)
            if result:
                print(result)
            
            print("\n=== Process Tree ===")
            result = analyzer.process_tree(args.analyze)
            if result:
                print(result)
    else:
        # Connect to DMA device and dump memory
        client = DMAClient(args.host, args.port)
        
        if not client.connect():
            sys.exit(1)
        
        # Test connection
        if not client.ping():
            client.disconnect()
            sys.exit(1)
        
        # Get memory layout
        print("Getting memory layout...")
        regions = client.get_memory_layout()
        for region in regions:
            print(f"Region: 0x{region['start']:016x} - 0x{region['end']:016x} "
                  f"(Size: {region['size']//1024//1024}MB, Flags: 0x{region['flags']:x})")
        
        # Dump memory
        start_addr = int(args.start_addr, 0)
        dump_size = parse_size(args.dump_size)
        
        if client.dump_memory(start_addr, dump_size, args.output):
            print(f"Memory dump saved to {args.output}")
            
            # Analyze with Volatility if requested
            if not (args.list_processes or args.process_tree):
                print("\nStarting Volatility analysis...")
                analyzer = VolatilityAnalyzer(args.volatility_path)
                
                print("=== Process List ===")
                result = analyzer.list_processes(args.output)
                if result:
                    print(result)
        
        client.disconnect()

if __name__ == '__main__':
    main()

/*
 * Physical memory access helper for QEMU
 */

#ifndef CPU_PHYSICAL_MEMORY_H
#define CPU_PHYSICAL_MEMORY_H

#include "qemu/osdep.h"

/*
 * cpu_physical_memory_rw - Access physical memory
 * @addr: Physical memory address to access
 * @buf: Buffer to read into or write from
 * @len: Length of the buffer
 * @is_write: 0 for read, 1 for write
 *
 * Returns 0 on success, non-zero on error
 */
int cpu_physical_memory_rw(hwaddr addr, uint8_t *buf, hwaddr len, int is_write);

#endif /* CPU_PHYSICAL_MEMORY_H */

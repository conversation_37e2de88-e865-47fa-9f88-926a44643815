#!/usr/bin/env python3
"""
Volatility 3 Setup Script
Downloads and sets up Volatility 3 for memory analysis
"""

import os
import sys
import subprocess
import urllib.request
import zipfile
import tempfile
from pathlib import Path

def run_command(cmd, cwd=None):
    """Run a command and return success status"""
    try:
        result = subprocess.run(cmd, shell=True, cwd=cwd, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"Command failed: {cmd}")
            print(f"Error: {result.stderr}")
            return False
        return True
    except Exception as e:
        print(f"Failed to run command: {cmd}")
        print(f"Error: {e}")
        return False

def download_file(url, filename):
    """Download a file from URL"""
    try:
        print(f"Downloading {filename}...")
        urllib.request.urlretrieve(url, filename)
        return True
    except Exception as e:
        print(f"Failed to download {filename}: {e}")
        return False

def setup_volatility3():
    """Setup Volatility 3"""
    print("Setting up Volatility 3...")
    
    # Create volatility directory
    vol_dir = Path("volatility3")
    if vol_dir.exists():
        print("Volatility 3 directory already exists")
    else:
        print("Cloning Volatility 3 repository...")
        if not run_command("git clone https://github.com/volatilityfoundation/volatility3.git"):
            print("Failed to clone repository. Trying alternative method...")
            # Try downloading as zip
            if not download_file("https://github.com/volatilityfoundation/volatility3/archive/develop.zip", "volatility3.zip"):
                return False
            
            print("Extracting Volatility 3...")
            with zipfile.ZipFile("volatility3.zip", 'r') as zip_ref:
                zip_ref.extractall(".")
            os.rename("volatility3-develop", "volatility3")
            os.remove("volatility3.zip")
    
    # Install dependencies
    print("Installing Volatility 3 dependencies...")
    if not run_command("pip install -e .", cwd="volatility3"):
        print("Failed to install dependencies. Trying with --user flag...")
        if not run_command("pip install --user -e .", cwd="volatility3"):
            return False
    
    # Download Windows symbols
    symbols_dir = vol_dir / "volatility3" / "symbols"
    symbols_dir.mkdir(exist_ok=True)
    
    windows_symbols = symbols_dir / "windows.zip"
    if not windows_symbols.exists():
        print("Downloading Windows symbol tables...")
        if not download_file("https://downloads.volatilityfoundation.org/volatility3/symbols/windows.zip", 
                           str(windows_symbols)):
            print("Warning: Failed to download Windows symbols. Manual download may be required.")
    
    print("Volatility 3 setup completed!")
    print(f"Volatility 3 installed in: {vol_dir.absolute()}")
    print(f"Run with: python3 {vol_dir / 'vol.py'} -h")
    
    return True

def test_volatility3():
    """Test Volatility 3 installation"""
    print("Testing Volatility 3 installation...")
    
    vol_path = Path("volatility3") / "vol.py"
    if not vol_path.exists():
        print("vol.py not found!")
        return False
    
    # Test basic functionality
    if run_command(f"python3 {vol_path} -h"):
        print("Volatility 3 is working correctly!")
        return True
    else:
        print("Volatility 3 test failed!")
        return False

def create_test_script():
    """Create a test script for memory analysis"""
    test_script = """#!/usr/bin/env python3
# Test script for Volatility 3 memory analysis

import subprocess
import sys
from pathlib import Path

def test_memory_analysis(memory_file):
    vol_path = Path("volatility3") / "vol.py"
    
    if not vol_path.exists():
        print("Volatility 3 not found. Run setup_volatility3.py first.")
        return
    
    if not Path(memory_file).exists():
        print(f"Memory file {memory_file} not found.")
        return
    
    print("Testing Volatility 3 with memory file...")
    
    # Test windows.info
    print("\\n=== System Information ===")
    subprocess.run([sys.executable, str(vol_path), "-f", memory_file, "windows.info"])
    
    # Test windows.pslist
    print("\\n=== Process List ===")
    subprocess.run([sys.executable, str(vol_path), "-f", memory_file, "windows.pslist"])

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python3 test_volatility.py <memory_file>")
        sys.exit(1)
    
    test_memory_analysis(sys.argv[1])
"""
    
    with open("test_volatility.py", "w") as f:
        f.write(test_script)
    
    os.chmod("test_volatility.py", 0o755)
    print("Created test_volatility.py script")

def main():
    print("Volatility 3 Setup Script")
    print("=" * 40)
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("Error: Python 3.8 or later is required")
        sys.exit(1)
    
    # Check if git is available
    if not run_command("git --version"):
        print("Warning: git not found. Will try alternative download method.")
    
    # Setup Volatility 3
    if not setup_volatility3():
        print("Failed to setup Volatility 3")
        sys.exit(1)
    
    # Test installation
    if not test_volatility3():
        print("Volatility 3 test failed")
        sys.exit(1)
    
    # Create test script
    create_test_script()
    
    print("\nSetup completed successfully!")
    print("\nNext steps:")
    print("1. Test with: python3 dma_memory_analyzer.py --help")
    print("2. Connect to QEMU DMA device and dump memory")
    print("3. Analyze memory dump with Volatility 3")
    print("\nExample usage:")
    print("python3 dma_memory_analyzer.py --host <qemu_host> --dump-size 4GB --output memory.raw")
    print("python3 dma_memory_analyzer.py --analyze memory.raw")

if __name__ == "__main__":
    main()

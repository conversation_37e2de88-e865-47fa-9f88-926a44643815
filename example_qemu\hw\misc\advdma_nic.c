/*
 * Streamlined DMA Device for Memory Analysis
 * Provides core DMA functionality for external memory analysis tools
 *
 * Features:
 * - Physical memory read/write access
 * - Bulk memory dumping for external analysis
 * - Network API for external tools (Volatility 3, MemProcFS, etc.)
 * - Virtual address translation support
 *
 * Place in: qemu/hw/misc/
 * License: GPL-2.0+
 */

#include "qemu/osdep.h"
#include "qemu/cutils.h"
#include "hw/pci/pci.h"
#include "hw/pci/msi.h"
#include "net/net.h"
#include "qemu/module.h"
#include "qemu/timer.h"
#include "qemu/sockets.h"
#include "qemu/thread.h"
#include "io/channel-socket.h"
#include "hw/qdev-properties.h"
#include "qapi/error.h"
#include "exec/cpu-common.h"
#include "qemu/error-report.h"
#include "qemu/log.h"
#include "qemu/queue.h"
#include "exec/cpu-all.h"
#include "target/i386/cpu.h"
#include "cpu_physical_memory.h"
#include "qemu/log.h"
#include "qemu/queue.h"
#include <inttypes.h>

#define TYPE_ADVDMA_NIC "advdma-nic"
#define ADVDMA_NIC(obj) OBJECT_CHECK(AdvDMANICState, (obj), TYPE_ADVDMA_NIC)

/* Custom DMA Device */
#define PCI_VENDOR_ID_CUSTOM        0x1337
#define PCI_DEVICE_ID_DMA           0x0001
#define ADVDMA_DEFAULT_PORT         31338
#define ADVDMA_MAGIC                0x444D4100  /* 'DMA\0' */

/* Core command definitions */
#define CMD_PING                    0x1000
#define CMD_READ_PHYS               0x2000
#define CMD_WRITE_PHYS              0x2001
#define CMD_TRANSLATE_VA            0x2002
#define CMD_DUMP_MEMORY             0x3000
#define CMD_GET_MEMORY_LAYOUT       0x3001

/* Maximum limits */
#define MAX_TRANSFER_SIZE           0x100000  /* 1MB */
#define DUMP_CHUNK_SIZE             0x100000  /* 1MB chunks for memory dumping */
#define MAX_MEMORY_REGIONS          64        /* Maximum memory regions to track */

/* Protocol structures */
typedef struct {
    uint32_t magic;
    uint32_t command;
    uint32_t status;
    uint32_t data_size;
    uint64_t param1;
    uint64_t param2;
    uint64_t param3;
    uint32_t reserved[4];
} __attribute__((packed)) AdvDMAPacket;

typedef struct {
    uint64_t start_addr;
    uint64_t size;
    uint32_t flags;
    uint32_t reserved;
} __attribute__((packed)) MemoryRegion;

typedef struct AdvDMANICState {
    PCIDevice parent_obj;
    MemoryRegion mmio;
    QIOChannelSocket *server;
    QemuThread thread;
    bool running;
    uint32_t port;
    QemuMutex lock;

    /* Memory layout information */
    MemoryRegion memory_regions[MAX_MEMORY_REGIONS];
    uint32_t num_memory_regions;

    /* Statistics */
    uint64_t bytes_read;
    uint64_t bytes_written;
    uint64_t packets_processed;
} AdvDMANICState;

/* Function declarations */
static void init_memory_layout(AdvDMANICState *s);
static void handle_client(AdvDMANICState *s, QIOChannelSocket *client);
static void *network_thread(void *opaque);

/* Core memory access functions */
static bool read_phys_mem(uint64_t addr, void *buf, size_t size) {
    CPUState *cpu = qemu_get_cpu(0);
    if (!cpu) {
        return false;
    }
    int ret = cpu_physical_memory_rw(addr, buf, size, 0); /* 0 = read */
    return (ret == 0);
}

static bool write_phys_mem(uint64_t addr, const void *buf, size_t size) {
    CPUState *cpu = qemu_get_cpu(0);
    if (!cpu) {
        return false;
    }
    int ret = cpu_physical_memory_rw(addr, (uint8_t *)buf, size, 1); /* 1 = write */
    return (ret == 0);
}

/* Page table walking for virtual address translation */
static uint64_t translate_virtual_address(uint64_t cr3, uint64_t va) {
    uint64_t pml4_index = (va >> 39) & 0x1FF;
    uint64_t pdpt_index = (va >> 30) & 0x1FF;
    uint64_t pd_index = (va >> 21) & 0x1FF;
    uint64_t pt_index = (va >> 12) & 0x1FF;
    uint64_t page_offset = va & 0xFFF;

    /* PML4 (Page Map Level 4) */
    uint64_t pml4_base = cr3 & 0xFFFFFFFFFFFFF000ULL;
    uint64_t pml4_entry;
    if (!read_phys_mem(pml4_base + pml4_index * 8, &pml4_entry, 8) || !(pml4_entry & 1)) {
        return 0;
    }

    /* PDPT (Page Directory Pointer Table) */
    uint64_t pdpt_base = pml4_entry & 0xFFFFFFFFFFFFF000ULL;
    uint64_t pdpt_entry;
    if (!read_phys_mem(pdpt_base + pdpt_index * 8, &pdpt_entry, 8) || !(pdpt_entry & 1)) {
        return 0;
    }

    /* Check for 1GB page */
    if (pdpt_entry & 0x80) {
        return (pdpt_entry & 0xFFFFFFFFC0000000ULL) | (va & 0x3FFFFFFFULL);
    }

    /* PD (Page Directory) */
    uint64_t pd_base = pdpt_entry & 0xFFFFFFFFFFFFF000ULL;
    uint64_t pd_entry;
    if (!read_phys_mem(pd_base + pd_index * 8, &pd_entry, 8) || !(pd_entry & 1)) {
        return 0;
    }

    /* Check for 2MB page */
    if (pd_entry & 0x80) {
        return (pd_entry & 0xFFFFFFFFFFFE0000ULL) | (va & 0x1FFFFFULL);
    }

    /* PT (Page Table) */
    uint64_t pt_base = pd_entry & 0xFFFFFFFFFFFFF000ULL;
    uint64_t pt_entry;
    if (!read_phys_mem(pt_base + pt_index * 8, &pt_entry, 8) || !(pt_entry & 1)) {
        return 0;
    }

    return (pt_entry & 0xFFFFFFFFFFFFF000ULL) | page_offset;
}

/* Initialize memory layout information */
static void init_memory_layout(AdvDMANICState *s) {
    qemu_log("AdvDMA: Initializing memory layout information\n");

    /* Initialize basic memory regions for common system layouts */
    s->num_memory_regions = 0;

    /* Low memory region (0-1MB) */
    s->memory_regions[s->num_memory_regions].start_addr = 0x0;
    s->memory_regions[s->num_memory_regions].size = 0x100000;
    s->memory_regions[s->num_memory_regions].flags = 0x1; /* System */
    s->num_memory_regions++;

    /* Standard RAM regions (1MB-4GB) */
    s->memory_regions[s->num_memory_regions].start_addr = 0x100000;
    s->memory_regions[s->num_memory_regions].size = 0xFFF00000;
    s->memory_regions[s->num_memory_regions].flags = 0x2; /* RAM */
    s->num_memory_regions++;

    /* High memory region (4GB+) - will be dynamically detected */
    s->memory_regions[s->num_memory_regions].start_addr = 0x100000000ULL;
    s->memory_regions[s->num_memory_regions].size = 0x400000000ULL; /* 16GB max */
    s->memory_regions[s->num_memory_regions].flags = 0x2; /* RAM */
    s->num_memory_regions++;

    qemu_log("AdvDMA: Initialized %d memory regions\n", s->num_memory_regions);
}





/* Handle client connection and process commands */
static void handle_client(AdvDMANICState *s, QIOChannelSocket *client) {
    AdvDMAPacket packet;
    Error *err = NULL;
    
    qemu_log("AdvDMA NIC: Client connected from socket\n");
    
    while (true) {
        // Clear packet structure
        memset(&packet, 0, sizeof(packet));
        
        // Receive packet header
        if (qio_channel_read_all(QIO_CHANNEL(client), (char *)&packet, sizeof(packet), &err) < 0 || err) {
            if (err) {
                qemu_log("AdvDMA NIC: Client read error: %s\n", error_get_pretty(err));
                error_free(err);
            } else {
                qemu_log("AdvDMA NIC: Client disconnected\n");
            }
            break;
        }
        
        // Validate magic number
        if (packet.magic != ADVDMA_MAGIC) {
            qemu_log("AdvDMA NIC: Invalid magic number: 0x%08X\n", packet.magic);
            continue;
        }
        
        qemu_log("AdvDMA NIC: Received command 0x%04X\n", packet.command);
        
        // Prepare response
        AdvDMAPacket response = {
            .magic = ADVDMA_MAGIC,
            .command = packet.command,
            .status = 1,  // Default to error
            .data_size = 0,
            .param1 = 0,
            .param2 = 0,
            .param3 = 0
        };
        
        // Process commands
        switch (packet.command) {
        case CMD_PING:
            qemu_log("AdvDMA NIC: Processing PING command\n");
            response.status = 0;            response.param1 = 0x504F4E47; // 'PONG'
            break;
            
        case CMD_GET_KERNEL_BASE: {
            qemu_log("AdvDMA NIC: Processing GET_KERNEL_BASE command\n");
            uint64_t kernel_base = find_kernel_base(s);
            if (kernel_base) {
                response.status = 0;
                response.param1 = kernel_base;
                qemu_log("AdvDMA NIC: Found kernel base: 0x%016" PRIx64 "\n", kernel_base);
            } else {
                qemu_log("AdvDMA NIC: Failed to find kernel base\n");
                response.status = 1;
            }
            break;
        }
        
        case CMD_READ_PHYS: {
            uint64_t address = packet.param1;
            uint32_t size = (uint32_t)packet.param2;
            qemu_log("AdvDMA NIC: Processing READ_PHYS: addr=0x%016" PRIx64 ", size=0x%X\n", address, size);
            
            if (size > 0 && size <= MAX_TRANSFER_SIZE) {
                uint8_t *buffer = g_malloc(size);
                if (read_phys_mem(address, buffer, size)) {
                    response.status = 0;
                    response.data_size = size;
                    
                    // Send response header
                    qio_channel_write_all(QIO_CHANNEL(client), (char *)&response, sizeof(response), &err);
                    // Send data
                    qio_channel_write_all(QIO_CHANNEL(client), (char *)buffer, size, &err);
                    g_free(buffer);
                    continue; // Skip sending response again
                } else {
                    qemu_log("AdvDMA NIC: Physical memory read failed\n");
                    response.status = 1;
                }
                g_free(buffer);
            } else {
                qemu_log("AdvDMA NIC: Invalid read size: 0x%X\n", size);
                response.status = 2; // Invalid size
            }
            break;
        }
        
        case CMD_WRITE_PHYS: {
            uint64_t address = packet.param1;
            uint32_t size = (uint32_t)packet.param2;
            qemu_log("AdvDMA NIC: Processing WRITE_PHYS: addr=0x%016" PRIx64 ", size=0x%X\n", address, size);
            
            if (size > 0 && size <= MAX_TRANSFER_SIZE && packet.data_size == size) {
                uint8_t *buffer = g_malloc(size);
                if (qio_channel_read_all(QIO_CHANNEL(client), (char *)buffer, size, &err) >= 0) {
                    if (write_phys_mem(address, buffer, size)) {
                        response.status = 0;
                        qemu_log("AdvDMA NIC: Physical memory write successful\n");
                    } else {
                        qemu_log("AdvDMA NIC: Physical memory write failed\n");
                        response.status = 1;
                    }
                } else {
                    qemu_log("AdvDMA NIC: Failed to read write data from client\n");
                    response.status = 3;
                }
                g_free(buffer);
            } else {
                qemu_log("AdvDMA NIC: Invalid write parameters\n");
                response.status = 2;
            }
            break;
        }
        
        case CMD_READ_VIRT: {
            uint64_t va = packet.param1;
            uint32_t size = (uint32_t)packet.param2;
            uint64_t cr3 = packet.param3;
            qemu_log("AdvDMA NIC: Processing READ_VIRT: va=0x%016" PRIx64 ", size=0x%X, cr3=0x%016" PRIx64 "\n", va, size, cr3);
            
            if (size > 0 && size <= MAX_TRANSFER_SIZE) {
                uint64_t phys_addr = translate_virtual_address(cr3, va);
                if (phys_addr) {
                    uint8_t *buffer = g_malloc(size);
                    if (read_phys_mem(phys_addr, buffer, size)) {
                        response.status = 0;
                        response.data_size = size;
                        
                        // Send response header
                        qio_channel_write_all(QIO_CHANNEL(client), (char *)&response, sizeof(response), &err);
                        // Send data
                        qio_channel_write_all(QIO_CHANNEL(client), (char *)buffer, size, &err);
                        g_free(buffer);
                        continue;
                    } else {
                        qemu_log("AdvDMA NIC: Virtual memory read failed (physical read)\n");
                        response.status = 1;
                    }
                    g_free(buffer);
                } else {
                    qemu_log("AdvDMA NIC: Virtual address translation failed\n");
                    response.status = 4; // Translation failed
                }
            } else {
                qemu_log("AdvDMA NIC: Invalid virtual read size\n");
                response.status = 2;
            }
            break;
        }
        
        case CMD_WRITE_VIRT: {
            uint64_t va = packet.param1;
            uint32_t size = (uint32_t)packet.param2;
            uint64_t cr3 = packet.param3;
            qemu_log("AdvDMA NIC: Processing WRITE_VIRT: va=0x%016" PRIx64 ", size=0x%X, cr3=0x%016" PRIx64 "\n", va, size, cr3);
            
            if (size > 0 && size <= MAX_TRANSFER_SIZE && packet.data_size == size) {
                uint64_t phys_addr = translate_virtual_address(cr3, va);
                if (phys_addr) {
                    uint8_t *buffer = g_malloc(size);
                    if (qio_channel_read_all(QIO_CHANNEL(client), (char *)buffer, size, &err) >= 0) {
                        if (write_phys_mem(phys_addr, buffer, size)) {
                            response.status = 0;
                            qemu_log("AdvDMA NIC: Virtual memory write successful\n");
                        } else {
                            qemu_log("AdvDMA NIC: Virtual memory write failed\n");
                            response.status = 1;
                        }
                    } else {
                        qemu_log("AdvDMA NIC: Failed to read write data\n");
                        response.status = 3;
                    }
                    g_free(buffer);
                } else {
                    qemu_log("AdvDMA NIC: Virtual address translation failed\n");
                    response.status = 4;
                }
            } else {
                qemu_log("AdvDMA NIC: Invalid virtual write parameters\n");
                response.status = 2;
            }
            break;
        }
        
        case CMD_TRANSLATE_VA: {
            uint64_t va = packet.param1;
            uint64_t cr3 = packet.param2;
            qemu_log("AdvDMA NIC: Processing TRANSLATE_VA: va=0x%016" PRIx64 ", cr3=0x%016" PRIx64 "\n", va, cr3);
            
            uint64_t phys_addr = translate_virtual_address(cr3, va);
            if (phys_addr) {
                response.status = 0;
                response.param1 = phys_addr;
                qemu_log("AdvDMA NIC: Translation successful: 0x%016" PRIx64 " -> 0x%016" PRIx64 "\n", va, phys_addr);
            } else {
                qemu_log("AdvDMA NIC: Translation failed\n");
                response.status = 1;
            }
            break;
        }        case CMD_ENUM_PROCESSES: {
            qemu_log("AdvDMA NIC: Processing ENUM_PROCESSES command\n");
            ProcessInfo *processes = g_malloc(sizeof(ProcessInfo) * MAX_PROCESSES);
            int count = enumerate_processes(s, processes, MAX_PROCESSES);
            if (count > 0) {
                response.status = 0;
                response.data_size = count * sizeof(ProcessInfo);
                response.param1 = count;
                qio_channel_write_all(QIO_CHANNEL(client), (char *)&response, sizeof(response), &err);
                qio_channel_write_all(QIO_CHANNEL(client), (char *)processes, response.data_size, &err);
                g_free(processes);
                continue;
            } else {
                response.status = 1;
            }
            g_free(processes);
            break;
        }        case CMD_ENUM_MODULES: {
            uint64_t cr3 = packet.param1;
            uint64_t peb = packet.param2;
            bool is_wow64 = (packet.param3 != 0);
            qemu_log("AdvDMA NIC: Processing ENUM_MODULES: cr3=0x%016" PRIx64 ", peb=0x%016" PRIx64 ", wow64=%d\n", cr3, peb, is_wow64);
            
            ModuleInfo *modules = g_malloc(sizeof(ModuleInfo) * MAX_MODULES);
            int count = enumerate_modules(s, cr3, peb, is_wow64, modules, MAX_MODULES);
            if (count > 0) {
                response.status = 0;
                response.data_size = count * sizeof(ModuleInfo);
                response.param1 = count;
                qio_channel_write_all(QIO_CHANNEL(client), (char *)&response, sizeof(response), &err);
                qio_channel_write_all(QIO_CHANNEL(client), (char *)modules, response.data_size, &err);
                g_free(modules);
                continue;
            } else {
                response.status = 1;
            }
            g_free(modules);
            break;
        }        case CMD_GET_EXPORT: {
            uint64_t cr3 = packet.param1;
            uint64_t module_base = packet.param2;
            qemu_log("AdvDMA NIC: Processing GET_EXPORT: cr3=0x%016" PRIx64 ", module=0x%016" PRIx64 ", size=%u\n", 
                     cr3, module_base, packet.data_size);
            
            if (packet.data_size > 0 && packet.data_size < 256) {
                char *function_name = g_malloc(packet.data_size + 1);
                if (qio_channel_read_all(QIO_CHANNEL(client), function_name, packet.data_size, &err) >= 0) {
                    function_name[packet.data_size] = 0;
                    qemu_log("AdvDMA NIC: Looking for export: %s\n", function_name);
                    
                    uint64_t func_addr = get_export_address(s, cr3, module_base, function_name);
                    if (func_addr) {
                        response.status = 0;
                        response.param1 = func_addr;
                        qemu_log("AdvDMA NIC: Export found at: 0x%016" PRIx64 "\n", func_addr);
                    } else {
                        response.status = 1;
                        qemu_log("AdvDMA NIC: Export not found\n");
                    }
                } else {
                    qemu_log("AdvDMA NIC: Failed to read function name\n");
                    response.status = 3;
                }
                g_free(function_name);
            } else {
                qemu_log("AdvDMA NIC: Invalid function name size: %u\n", packet.data_size);
                response.status = 2;
            }
            break;
        }        case CMD_BULK_SCAN: {
            qemu_log("AdvDMA NIC: Processing BULK_SCAN command\n");
            uint64_t address = packet.param1;
            uint32_t size = (uint32_t)packet.param2;
            uint32_t max_results = (uint32_t)packet.param3;
            
            // Special case: PsActiveProcessHead fast scan (param1 = 0, param2 = 0)
            if (address == 0 && size == 0) {
                qemu_log("AdvDMA NIC: Performing multi-threaded PsActiveProcessHead scan\n");
                BulkScanResult *results = g_malloc(sizeof(BulkScanResult) * max_results);
                uint32_t found_count = fast_pshead_scan_mt(s, results, max_results);
                
                if (found_count > 0) {
                    response.status = 0;
                    response.param1 = found_count;
                    response.data_size = found_count * sizeof(BulkScanResult);
                    
                    // Send response header
                    qio_channel_write_all(QIO_CHANNEL(client), (char *)&response, sizeof(response), &err);
                    // Send results
                    qio_channel_write_all(QIO_CHANNEL(client), (char *)results, response.data_size, &err);
                    g_free(results);
                    continue; // Skip sending response again
                } else {
                    qemu_log("AdvDMA NIC: Multi-threaded scan found no candidates\n");
                    response.status = 1;
                }
                g_free(results);
            }
            // Regular bulk memory read
            else if (size > 0 && size <= MAX_TRANSFER_SIZE && max_results > 0 && max_results <= MAX_SCAN_RESULTS) {
                uint8_t *buffer = g_malloc(size);
                if (read_phys_mem(address, buffer, size)) {
                    response.status = 0;
                    response.data_size = size;
                    
                    // Send response header
                    qio_channel_write_all(QIO_CHANNEL(client), (char *)&response, sizeof(response), &err);
                    // Send data
                    qio_channel_write_all(QIO_CHANNEL(client), (char *)buffer, size, &err);
                    g_free(buffer);
                    continue; // Skip sending response again
                } else {
                    qemu_log("AdvDMA NIC: Bulk memory read failed\n");
                    response.status = 1;
                }
                g_free(buffer);
            } else {
                qemu_log("AdvDMA NIC: Invalid bulk scan parameters\n");
                response.status = 2;
            }
            break;
        }case CMD_PATTERN_SCAN: {
            qemu_log("AdvDMA NIC: Processing PATTERN_SCAN command\n");
            uint64_t start_addr = packet.param1;
            uint64_t end_addr = packet.param2;
            uint32_t pattern_size = (uint32_t)packet.param3;
            
            if (pattern_size > 0 && pattern_size <= sizeof(PatternScanRequest) - 24) {
                uint8_t *pattern_buffer = g_malloc(pattern_size);
                if (qio_channel_read_all(QIO_CHANNEL(client), (char *)pattern_buffer, pattern_size, &err) >= 0) {
                    // Perform the pattern scan in chunks
                    uint64_t addr = start_addr;
                    uint32_t chunk_size = BULK_SCAN_CHUNK_SIZE;
                    uint32_t total_found = 0;
                    
                    while (addr < end_addr && total_found < MAX_SCAN_RESULTS) {
                        if (addr + chunk_size > end_addr) {
                            chunk_size = end_addr - addr;
                        }
                        
                        uint8_t *scan_buffer = g_malloc(chunk_size);
                        if (read_phys_mem(addr, scan_buffer, chunk_size)) {
                            // Scan the chunk for the pattern
                            for (uint32_t offset = 0; offset <= chunk_size - pattern_size; offset++) {
                                if (memcmp(scan_buffer + offset, pattern_buffer, pattern_size) == 0) {
                                    // Pattern found
                                    uint64_t found_addr = addr + offset;
                                    
                                    // Send result immediately
                                    BulkScanResult result = {
                                        .address = found_addr,
                                        .value = 0,  // Value not applicable for pattern scan
                                        .confidence = 100,  // High confidence for exact match
                                        .reserved = 0
                                    };
                                    qio_channel_write_all(QIO_CHANNEL(client), (char *)&result, sizeof(result), &err);
                                    total_found++;
                                    
                                    if (total_found >= MAX_SCAN_RESULTS) {
                                        break;
                                    }
                                }
                            }
                        } else {
                            qemu_log("AdvDMA NIC: Pattern scan read failed at 0x%016" PRIx64 "\n", addr);
                        }
                        g_free(scan_buffer);
                        addr += chunk_size;
                    }
                    
                    response.status = 0;
                    response.param1 = total_found;
                } else {
                    qemu_log("AdvDMA NIC: Failed to read pattern data\n");
                    response.status = 3;
                }
                g_free(pattern_buffer);
            } else {
                qemu_log("AdvDMA NIC: Invalid pattern scan parameters\n");
                response.status = 2;
            }
            break;
        }
        
        default:
            qemu_log("AdvDMA NIC: Unknown command: 0x%04X\n", packet.command);
            response.status = 0xFF; // Unknown command
            break;
        }
        
        // Send response for commands that don't send data
        qio_channel_write_all(QIO_CHANNEL(client), (char *)&response, sizeof(response), &err);
        if (err) {
            qemu_log("AdvDMA NIC: Failed to send response: %s\n", error_get_pretty(err));
            error_free(err);
            break;
        }    }
    
    object_unref(OBJECT(client));
    qemu_log("AdvDMA NIC: Client handler finished\n");
}

/* Network thread function */
static void *network_thread(void *opaque) {
    AdvDMANICState *s = (AdvDMANICState *)opaque;
    SocketAddress addr = { .type = SOCKET_ADDRESS_TYPE_INET };
    addr.u.inet.host = g_strdup("0.0.0.0");
    addr.u.inet.port = g_strdup_printf("%d", s->port);
    
    QIOChannelSocket *listener = qio_channel_socket_new();
    Error *err = NULL;
    
    qemu_log("AdvDMA NIC: Starting network thread on port %d\n", s->port);
    
    if (qio_channel_socket_listen_sync(listener, &addr, 1, &err) < 0) {
        error_report("AdvDMA NIC: Failed to listen on 0.0.0.0:%d", s->port);
        if (err) {
            error_report("AdvDMA NIC: Listen error: %s", error_get_pretty(err));
            error_free(err);
        }
        g_free(addr.u.inet.host);
        g_free(addr.u.inet.port);
        object_unref(OBJECT(listener));
        return NULL;
    }
    
    error_report("AdvDMA NIC: Successfully listening on 0.0.0.0:%d", s->port);
    qemu_log("AdvDMA NIC: Ready to accept connections\n");
    
    while (s->running) {
        QIOChannelSocket *client = qio_channel_socket_accept(listener, &err);
        if (!client) {
            if (err) {
                if (s->running) { // Only log if we're supposed to be running
                    qemu_log("AdvDMA NIC: Accept error: %s\n", error_get_pretty(err));
                }
                error_free(err);
                err = NULL;
            }
            // Small delay to prevent busy waiting
            g_usleep(10000); // 10ms            continue;
        }
        
        qemu_log("AdvDMA NIC: New client connection accepted\n");
        handle_client(s, client);
    }
    
    object_unref(OBJECT(listener));
    g_free(addr.u.inet.host);
    g_free(addr.u.inet.port);
    
    qemu_log("AdvDMA NIC: Network thread shutting down\n");
    return NULL;
}

/* Device initialization */
static void advdma_nic_realize(PCIDevice *pci_dev, Error **errp) {
    AdvDMANICState *s = ADVDMA_NIC(pci_dev);
    
    /* Initialize MMIO region (required for proper PCI device) */
    memory_region_init_io(&s->mmio, OBJECT(s), NULL, s, "advdma-mmio", 0x1000);
    pci_register_bar(pci_dev, 0, PCI_BASE_ADDRESS_SPACE_MEMORY, &s->mmio);
    
    /* Initialize state */
    s->port = ADVDMA_DEFAULT_PORT;
    s->running = true;
    s->kernel_info_cached = false;
    s->kernel_base = 0;
    s->ps_active_process_head = 0;
      qemu_mutex_init(&s->lock);
    
    /* Initialize thread pool for multi-threaded scanning */
    init_scan_thread_pool(s);
    
    /* Start network thread */
    qemu_thread_create(&s->thread, "advdma-net", network_thread, s, QEMU_THREAD_JOINABLE);
    
    qemu_log("AdvDMA NIC: Device initialized on port %d\n", s->port);
}

/* Device cleanup */
static void advdma_nic_exit(PCIDevice *pci_dev) {
    AdvDMANICState *s = ADVDMA_NIC(pci_dev);
    
    s->running = false;
    
    /* Shutdown thread pool first */
    shutdown_scan_thread_pool(s);
    
    /* Wait for network thread */
    qemu_thread_join(&s->thread);
    qemu_mutex_destroy(&s->lock);
    
    qemu_log("AdvDMA NIC: Device cleaned up\n");
}

/* Device class initialization */
static void advdma_nic_class_init(ObjectClass *klass, const void *data) {
    DeviceClass *dc = DEVICE_CLASS(klass);
    PCIDeviceClass *k = PCI_DEVICE_CLASS(klass);
    
    k->realize = advdma_nic_realize;
    k->exit = advdma_nic_exit;
    k->vendor_id = PCI_VENDOR_ID_INTEL;
    k->device_id = PCI_DEVICE_ID_82574L;
    k->revision = 0x00;
    k->class_id = PCI_CLASS_NETWORK_ETHERNET;
    
    dc->desc = "Advanced DMA Intel 82574L Gigabit Ethernet Controller";
    set_bit(DEVICE_CATEGORY_NETWORK, dc->categories);
}

/* Type information */
static const TypeInfo advdma_nic_info = {
    .name = TYPE_ADVDMA_NIC,
    .parent = TYPE_PCI_DEVICE,
    .instance_size = sizeof(AdvDMANICState),
    .class_init = advdma_nic_class_init,
    .interfaces = (InterfaceInfo[]) {
        { INTERFACE_CONVENTIONAL_PCI_DEVICE },
        { },
    },
};

/* Register device type */
static void advdma_nic_register_types(void) {
    type_register_static(&advdma_nic_info);
}

type_init(advdma_nic_register_types)

/* ================= MULTI-THREADED SCANNING IMPLEMENTATION ================= */

/* Thread pool worker function */
static void *scan_worker_thread(void *opaque) {
    AdvDMANICState *s = (AdvDMANICState *)opaque;
    ScanThreadPool *pool = &s->scan_pool;
    
    qemu_log("AdvDMA: Scan worker thread started\n");
    
    while (pool->pool_running) {
        ScanWorkItem *work = NULL;
        
        // Wait for work
        qemu_mutex_lock(&pool->queue_mutex);
        while (QTAILQ_EMPTY(&pool->work_queue) && pool->pool_running) {
            qemu_cond_wait(&pool->work_available, &pool->queue_mutex);
        }
        
        if (!pool->pool_running) {
            qemu_mutex_unlock(&pool->queue_mutex);
            break;
        }
        
        // Get work item
        work = QTAILQ_FIRST(&pool->work_queue);
        if (work) {
            QTAILQ_REMOVE(&pool->work_queue, work, next);
            pool->active_workers++;
        }
        qemu_mutex_unlock(&pool->queue_mutex);
        
        if (!work) continue;
        
        // Process the work item
        uint64_t addr = work->start_addr;
        uint64_t end_addr = work->end_addr;
        uint32_t chunk_size = work->chunk_size;
        
        qemu_log("AdvDMA: Worker scanning 0x%016" PRIx64 " - 0x%016" PRIx64 "\n", addr, end_addr);
        
        while (addr < end_addr && !(*work->scan_complete)) {
            uint64_t current_chunk_size = (end_addr - addr > chunk_size) ? chunk_size : (end_addr - addr);
            uint8_t *chunk_data = g_malloc(current_chunk_size);
            
            if (read_phys_mem(addr, chunk_data, current_chunk_size)) {
                // Scan the chunk for potential pointers
                for (uint64_t offset = 0; offset < current_chunk_size - 8; offset += 8) {
                    uint64_t *potential_ptr = (uint64_t*)(chunk_data + offset);
                    uint64_t value = *potential_ptr;
                    
                    // Check if this looks like a kernel pointer
                    if (value >= 0xFFFF800000000000ULL && value < 0xFFFFF00000000000ULL) {
                        // This could be PsActiveProcessHead - verify it points to process structures
                        uint64_t first_process;
                        if (read_phys_mem(value, (uint8_t*)&first_process, 8)) {
                            if (first_process >= 0xFFFF800000000000ULL && first_process < 0xFFFFF00000000000ULL) {
                                // Calculate confidence
                                uint32_t confidence = calculate_pshead_confidence(value, first_process);
                                
                                if (confidence >= 50) {  // Minimum confidence threshold
                                    qemu_mutex_lock(work->results_mutex);
                                    
                                    if (*work->result_count < work->max_results) {
                                        work->results[*work->result_count].address = addr + offset;
                                        work->results[*work->result_count].value = value;
                                        work->results[*work->result_count].confidence = confidence;
                                        (*work->result_count)++;
                                        
                                        qemu_log("AdvDMA: Worker found candidate at 0x%016" PRIx64 " -> 0x%016" PRIx64 " (confidence: %u%%)\n",
                                                 addr + offset, value, confidence);
                                    }
                                    
                                    qemu_mutex_unlock(work->results_mutex);
                                }
                            }
                        }
                    }
                }
            }
            
            g_free(chunk_data);
            addr += current_chunk_size;
        }
        
        // Mark work as complete
        qemu_mutex_lock(&pool->queue_mutex);
        pool->active_workers--;
        g_free(work);
        
        // Signal completion if no more work
        if (QTAILQ_EMPTY(&pool->work_queue) && pool->active_workers == 0) {
            qemu_cond_broadcast(&pool->work_complete);
        }
        qemu_mutex_unlock(&pool->queue_mutex);
    }
    
    qemu_log("AdvDMA: Scan worker thread finished\n");
    return NULL;
}

/* Initialize thread pool */
static void init_scan_thread_pool(AdvDMANICState *s) {
    ScanThreadPool *pool = &s->scan_pool;
    
    qemu_mutex_init(&pool->queue_mutex);
    qemu_cond_init(&pool->work_available);
    qemu_cond_init(&pool->work_complete);
    QTAILQ_INIT(&pool->work_queue);
    
    pool->pool_running = true;
    pool->active_workers = 0;
    pool->total_workers = SCAN_THREAD_POOL_SIZE;
    
    // Create worker threads
    for (int i = 0; i < SCAN_THREAD_POOL_SIZE; i++) {
        char thread_name[32];
        snprintf(thread_name, sizeof(thread_name), "advdma-scan%d", i);
        qemu_thread_create(&pool->threads[i], thread_name, scan_worker_thread, s, QEMU_THREAD_JOINABLE);
    }
    
    qemu_log("AdvDMA: Initialized scan thread pool with %d threads\n", SCAN_THREAD_POOL_SIZE);
}

/* Shutdown thread pool */
static void shutdown_scan_thread_pool(AdvDMANICState *s) {
    ScanThreadPool *pool = &s->scan_pool;
    
    qemu_log("AdvDMA: Shutting down scan thread pool\n");
    
    // Signal shutdown
    qemu_mutex_lock(&pool->queue_mutex);
    pool->pool_running = false;
    qemu_cond_broadcast(&pool->work_available);
    qemu_mutex_unlock(&pool->queue_mutex);
    
    // Wait for all threads to finish
    for (int i = 0; i < SCAN_THREAD_POOL_SIZE; i++) {
        qemu_thread_join(&pool->threads[i]);
    }
    
    // Clean up any remaining work
    ScanWorkItem *work, *next_work;
    QTAILQ_FOREACH_SAFE(work, &pool->work_queue, next, next_work) {
        QTAILQ_REMOVE(&pool->work_queue, work, next);
        g_free(work);
    }
    
    qemu_cond_destroy(&pool->work_available);
    qemu_cond_destroy(&pool->work_complete);
    qemu_mutex_destroy(&pool->queue_mutex);
    
    qemu_log("AdvDMA: Scan thread pool shutdown complete\n");
}

/* Multi-threaded PsActiveProcessHead scan */
static uint32_t fast_pshead_scan_mt(AdvDMANICState *s, BulkScanResult *results, uint32_t max_results) {
    // Get kernel base first for adaptive scanning
    uint64_t kernel_base = find_kernel_base(s);
    if (!kernel_base) {
        qemu_log("AdvDMA: Could not find kernel base for adaptive scanning\n");
        kernel_base = 0x0000000003800000; // Use default/common base
    }
    
    qemu_log("AdvDMA: Using kernel base 0x%016" PRIx64 " for adaptive multi-threaded scan\n", kernel_base);
    
    // Adaptive scan ranges based on actual kernel base
    // These ranges are relative to kernel base and cover typical PsActiveProcessHead locations
    uint64_t relative_ranges[][2] = {
        // .data section - where global variables are typically stored
        {0x480000, 0x500000},   // 512KB range in .data
        {0x500000, 0x580000},   // Extended .data range
        {0x580000, 0x600000},   // High .data range
        {0x600000, 0x680000},   // Very high .data range
        
        // .rdata section - read-only data that might contain pointers
        {0x10000, 0x90000},     // 512KB in .rdata
        {0x90000, 0x120000},    // Extended .rdata
        
        // Additional common locations
        {0x700000, 0x800000},   // 1MB range
        {0x800000, 0x900000},   // 1MB range
        {0x900000, 0xA00000},   // 1MB range
        {0xA00000, 0xB00000},   // 1MB range
    };
    
    // Also scan some absolute ranges that are commonly used regardless of kernel base
    uint64_t absolute_ranges[][2] = {
        {0x0000000003C80000, 0x0000000003D00000},  // Common kernel data area 1
        {0x0000000003D00000, 0x0000000003D80000},  // Common kernel data area 2
        {0x0000000003E00000, 0x0000000003E80000},  // Alternative location 1
        {0x0000000003F00000, 0x0000000003F80000},  // Alternative location 2
        {0x0000000004000000, 0x0000000004080000},  // Higher memory region 1
        {0x0000000004100000, 0x0000000004180000},  // Higher memory region 2
        {0x0000000004200000, 0x0000000004280000},  // Extended search 1
        {0x0000000004300000, 0x0000000004380000},  // Extended search 2
        {0x0000000004400000, 0x0000000004480000},  // Extended search 3
        {0x0000000004500000, 0x0000000004580000},  // Extended search 4
        {0x0000000005000000, 0x0000000005080000},  // High memory region 1
        {0x0000000005100000, 0x0000000005180000},  // High memory region 2
    };
    
    uint32_t found_count = 0;
    ScanThreadPool *pool = &s->scan_pool;
    
    QemuMutex results_mutex;
    qemu_mutex_init(&results_mutex);
    bool scan_complete = false;
    
    int total_ranges = sizeof(relative_ranges) / sizeof(relative_ranges[0]) + 
                      sizeof(absolute_ranges) / sizeof(absolute_ranges[0]);
    
    qemu_log("AdvDMA: Multi-threaded PsActiveProcessHead scan starting with %d ranges (kernel-relative + absolute)\n", total_ranges);
    
    // Distribute work across thread pool
    qemu_mutex_lock(&pool->queue_mutex);
    
    // Add kernel-relative ranges
    for (int i = 0; i < sizeof(relative_ranges) / sizeof(relative_ranges[0]); i++) {
        uint64_t start_addr = kernel_base + relative_ranges[i][0];
        uint64_t end_addr = kernel_base + relative_ranges[i][1];
        
        // Split large ranges into smaller work items for better load balancing
        uint64_t range_size = end_addr - start_addr;
        uint64_t work_chunk_size = range_size / (SCAN_THREAD_POOL_SIZE * 2); // Create 2x work items per thread
        if (work_chunk_size < BULK_SCAN_CHUNK_SIZE) {
            work_chunk_size = range_size; // Don't split if too small
        }
        
        for (uint64_t addr = start_addr; addr < end_addr; addr += work_chunk_size) {
            uint64_t work_end = addr + work_chunk_size;
            if (work_end > end_addr) work_end = end_addr;
            
            ScanWorkItem *work = g_malloc0(sizeof(ScanWorkItem));
            work->start_addr = addr;
            work->end_addr = work_end;
            work->chunk_size = BULK_SCAN_CHUNK_SIZE;
            work->results = results;
            work->result_count = &found_count;
            work->max_results = max_results;
            work->scan_complete = &scan_complete;
            work->results_mutex = &results_mutex;
            
            QTAILQ_INSERT_TAIL(&pool->work_queue, work, next);
        }
    }
    
    // Add absolute ranges
    for (int i = 0; i < sizeof(absolute_ranges) / sizeof(absolute_ranges[0]); i++) {
        uint64_t start_addr = absolute_ranges[i][0];
        uint64_t end_addr = absolute_ranges[i][1];
        
        // Split large ranges into smaller work items for better load balancing
        uint64_t range_size = end_addr - start_addr;
        uint64_t work_chunk_size = range_size / (SCAN_THREAD_POOL_SIZE * 2); // Create 2x work items per thread
        if (work_chunk_size < BULK_SCAN_CHUNK_SIZE) {
            work_chunk_size = range_size; // Don't split if too small
        }
        
        for (uint64_t addr = start_addr; addr < end_addr; addr += work_chunk_size) {
            uint64_t work_end = addr + work_chunk_size;
            if (work_end > end_addr) work_end = end_addr;
            
            ScanWorkItem *work = g_malloc0(sizeof(ScanWorkItem));
            work->start_addr = addr;
            work->end_addr = work_end;
            work->chunk_size = BULK_SCAN_CHUNK_SIZE;
            work->results = results;
            work->result_count = &found_count;
            work->max_results = max_results;
            work->scan_complete = &scan_complete;
            work->results_mutex = &results_mutex;
            
            QTAILQ_INSERT_TAIL(&pool->work_queue, work, next);
        }
    }
    
    // Wake up all workers
    qemu_cond_broadcast(&pool->work_available);
    
    // Wait for all work to complete
    while (!QTAILQ_EMPTY(&pool->work_queue) || pool->active_workers > 0) {
        qemu_cond_wait(&pool->work_complete, &pool->queue_mutex);
    }
    
    qemu_mutex_unlock(&pool->queue_mutex);
    qemu_mutex_destroy(&results_mutex);
    
    qemu_log("AdvDMA: Multi-threaded scan completed, found %d candidates\n", found_count);
    return found_count;
}

/* ================= END MULTI-THREADED SCANNING ================= */

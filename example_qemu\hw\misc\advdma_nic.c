/*
 * Advanced DMA Network Interface - Intel 82574L Emulation
 * Provides comprehensive Windows process/module enumeration and memory access
 * 
 * Features:
 * - Process enumeration via EPROCESS traversal
 * - Module enumeration (PEB->Ldr->InLoadOrderModuleList)
 * - PE export table parsing (GetProcAddress equivalent)
 * - Virtual/Physical memory access with page table walking
 * - WoW64 process support
 * 
 * Place in: qemu/hw/net/
 * License: GPL-2.0+
 */

#include "qemu/osdep.h"
#include "qemu/cutils.h"
#include "hw/pci/pci.h"
#include "hw/pci/msi.h"
#include "net/net.h"
#include "qemu/module.h"
#include "qemu/timer.h"
#include "qemu/sockets.h"
#include "qemu/thread.h"
#include "io/channel-socket.h"
#include "hw/qdev-properties.h"
#include "qapi/error.h"
#include "exec/cpu-common.h"
#include "qemu/error-report.h"
#include "qemu/log.h"
#include "qemu/queue.h"
#include "exec/cpu-all.h"
#include "target/i386/cpu.h"
#include "cpu_physical_memory.h"
#include "qemu/log.h"
#include "qemu/queue.h"
#include <inttypes.h>

#define TYPE_ADVDMA_NIC "advdma-nic"
#define ADVDMA_NIC(obj) OBJECT_CHECK(AdvDMANICState, (obj), TYPE_ADVDMA_NIC)

/* Intel 82574L Gigabit Ethernet Controller */
#define PCI_VENDOR_ID_INTEL         0x8086
#define PCI_DEVICE_ID_82574L        0x10D3
#define ADVDMA_DEFAULT_PORT         31338
#define ADVDMA_MAGIC                0x41444D41  /* 'ADMA' */

/* Command definitions */
#define CMD_PING                    0x1000
#define CMD_GET_KERNEL_BASE         0x1001
#define CMD_READ_PHYS               0x2000
#define CMD_WRITE_PHYS              0x2001
#define CMD_READ_VIRT               0x2002
#define CMD_WRITE_VIRT              0x2003
#define CMD_TRANSLATE_VA            0x2004
#define CMD_ENUM_PROCESSES          0x3000
#define CMD_ENUM_MODULES            0x3001
#define CMD_GET_EXPORT              0x3002
#define CMD_READ_PE_HEADER          0x3003
#define CMD_BULK_SCAN               0x4000
#define CMD_PATTERN_SCAN            0x4001

/* Windows kernel structure offsets (Windows 10 21H2 19044.5965) */
/* Source: https://www.vergiliusproject.com/kernels/x64/windows-10/21h2 */
#define EPROCESS_FLINK_OFFSET       0x448   /* ActiveProcessLinks.Flink */
#define EPROCESS_PID_OFFSET         0x440   /* UniqueProcessId */
#define EPROCESS_NAME_OFFSET        0x5a8   /* ImageFileName */
#define EPROCESS_CR3_OFFSET         0x28    /* DirectoryTableBase */
#define EPROCESS_PEB_OFFSET         0x550   /* Peb */
#define EPROCESS_WOW64_OFFSET       0x560   /* WoW64Process */

#define PEB_LDR_OFFSET              0x18    /* Ldr */
#define LDR_IN_LOAD_ORDER_OFFSET    0x10    /* InLoadOrderModuleList */
#define LDR_ENTRY_FLINK_OFFSET      0x0     /* Flink */
#define LDR_ENTRY_BASE_OFFSET       0x30    /* DllBase */
#define LDR_ENTRY_SIZE_OFFSET       0x40    /* SizeOfImage */
#define LDR_ENTRY_NAME_OFFSET       0x58    /* BaseDllName */

/* Maximum limits */
#define MAX_PROCESSES               256
#define MAX_MODULES                 512
#define MAX_TRANSFER_SIZE           0x100000  /* 1MB */
#define MAX_NAME_LENGTH             256
#define BULK_SCAN_CHUNK_SIZE        0x10000  /* 64KB chunks */
#define MAX_SCAN_RESULTS            256
#define SCAN_THREAD_POOL_SIZE       4       /* Number of scanning threads */

/* Protocol structures */
typedef struct {
    uint32_t magic;
    uint32_t command;
    uint32_t status;
    uint32_t data_size;
    uint64_t param1;
    uint64_t param2;
    uint64_t param3;
    uint32_t reserved[4];
} __attribute__((packed)) AdvDMAPacket;

typedef struct {
    uint64_t eprocess;
    uint32_t pid;
    uint32_t reserved;
    uint64_t cr3;
    uint64_t peb;
    uint64_t wow64_peb;
    char name[16];
} __attribute__((packed)) ProcessInfo;

typedef struct {
    uint64_t base_address;
    uint32_t size_of_image;
    uint32_t reserved;
    uint64_t entry_point;
    wchar_t name[MAX_NAME_LENGTH/2];
} __attribute__((packed)) ModuleInfo;

typedef struct {
    uint64_t function_address;
    uint32_t ordinal;
    uint32_t name_rva;
    char name[64];
} __attribute__((packed)) ExportInfo;

typedef struct {
    uint64_t address;
    uint64_t value;
    uint32_t confidence;  /* 0-100 confidence score */
    uint32_t reserved;
} __attribute__((packed)) BulkScanResult;

typedef struct {
    uint64_t start_addr;
    uint64_t end_addr;
    uint32_t pattern_size;
    uint32_t max_results;
    uint8_t pattern[32];  /* Pattern to search for */
} __attribute__((packed)) PatternScanRequest;

/* Multi-threaded scanning structures */
typedef struct ScanWorkItem {
    uint64_t start_addr;
    uint64_t end_addr;
    uint32_t chunk_size;
    BulkScanResult *results;
    uint32_t *result_count;
    uint32_t max_results;
    bool *scan_complete;
    QemuMutex *results_mutex;
    QTAILQ_ENTRY(ScanWorkItem) next;
} ScanWorkItem;

typedef struct {
    QemuThread threads[SCAN_THREAD_POOL_SIZE];
    QemuMutex queue_mutex;
    QemuCond work_available;
    QemuCond work_complete;
    QTAILQ_HEAD(, ScanWorkItem) work_queue;
    bool pool_running;
    int active_workers;
    int total_workers;
} ScanThreadPool;

typedef struct AdvDMANICState {
    PCIDevice parent_obj;
    MemoryRegion mmio;
    QIOChannelSocket *server;
    QemuThread thread;
    bool running;
    uint32_t port;
    QemuMutex lock;
    
    /* Multi-threaded scanning */
    ScanThreadPool scan_pool;
    
    /* Cached kernel information */
    uint64_t kernel_base;
    uint64_t ps_active_process_head;
    bool kernel_info_cached;
} AdvDMANICState;

/* Function declarations */
static bool verify_kernel_image(uint64_t base_addr, uint32_t pe_offset);
static uint32_t calculate_pshead_confidence(uint64_t list_head, uint64_t first_process);
static void init_scan_thread_pool(AdvDMANICState *s);
static void shutdown_scan_thread_pool(AdvDMANICState *s);
static uint32_t fast_pshead_scan_mt(AdvDMANICState *s, BulkScanResult *results, uint32_t max_results);
static void *scan_worker_thread(void *opaque);

/* Memory access functions using optimal QEMU APIs */
static bool read_phys_mem(uint64_t addr, void *buf, size_t size) {
    /* Use cpu_memory_rw_debug for best performance and reliability */
    CPUState *cpu = qemu_get_cpu(0);
    if (!cpu) {
        return false;
    }
    int ret = cpu_physical_memory_rw(addr, buf, size, 0); /* 0 = read */
    return (ret == 0);
}

static bool write_phys_mem(uint64_t addr, const void *buf, size_t size) {
    CPUState *cpu = qemu_get_cpu(0);
    if (!cpu) {
        return false;
    }
    int ret = cpu_physical_memory_rw(addr, (uint8_t *)buf, size, 1); /* 1 = write */
    return (ret == 0);
}

/* Advanced page table walking with support for all page sizes */
static uint64_t translate_virtual_address(uint64_t cr3, uint64_t va) {
    uint64_t pml4_index = (va >> 39) & 0x1FF;
    uint64_t pdpt_index = (va >> 30) & 0x1FF;
    uint64_t pd_index = (va >> 21) & 0x1FF;
    uint64_t pt_index = (va >> 12) & 0x1FF;
    uint64_t page_offset = va & 0xFFF;
    
    /* PML4 (Page Map Level 4) */
    uint64_t pml4_base = cr3 & 0xFFFFFFFFFFFFF000ULL;
    uint64_t pml4_entry;
    if (!read_phys_mem(pml4_base + pml4_index * 8, &pml4_entry, 8) || !(pml4_entry & 1)) {
        return 0;
    }
    
    /* PDPT (Page Directory Pointer Table) */
    uint64_t pdpt_base = pml4_entry & 0xFFFFFFFFFFFFF000ULL;
    uint64_t pdpt_entry;
    if (!read_phys_mem(pdpt_base + pdpt_index * 8, &pdpt_entry, 8) || !(pdpt_entry & 1)) {
        return 0;
    }
    
    /* Check for 1GB page */
    if (pdpt_entry & 0x80) {
        return (pdpt_entry & 0xFFFFFFFFC0000000ULL) | (va & 0x3FFFFFFFULL);
    }
    
    /* PD (Page Directory) */
    uint64_t pd_base = pdpt_entry & 0xFFFFFFFFFFFFF000ULL;
    uint64_t pd_entry;
    if (!read_phys_mem(pd_base + pd_index * 8, &pd_entry, 8) || !(pd_entry & 1)) {
        return 0;
    }
    
    /* Check for 2MB page */
    if (pd_entry & 0x80) {
        return (pd_entry & 0xFFFFFFFFFFFE0000ULL) | (va & 0x1FFFFFULL);
    }
    
    /* PT (Page Table) */
    uint64_t pt_base = pd_entry & 0xFFFFFFFFFFFFF000ULL;
    uint64_t pt_entry;
    if (!read_phys_mem(pt_base + pt_index * 8, &pt_entry, 8) || !(pt_entry & 1)) {
        return 0;
    }
    
    return (pt_entry & 0xFFFFFFFFFFFFF000ULL) | page_offset;
}

/* Advanced kernel base discovery using multiple techniques */
static uint64_t find_kernel_base(AdvDMANICState *s) {
    if (s->kernel_info_cached && s->kernel_base) {
        return s->kernel_base;
    }
    
    qemu_log("AdvDMA: Starting comprehensive kernel base discovery...\n");
    
    /* Method 1: Scan physical memory for Windows kernel PE headers */
    /* Windows kernel is typically loaded in high physical memory areas */
    uint64_t scan_ranges[] = {
        /* Low memory - sometimes kernel is here */
        0x1000000,      0x10000000,    /* 16MB - 256MB */
        /* High memory - modern Windows typically loads here */
        0x10000000,     0x40000000,    /* 256MB - 1GB */
        0x40000000,     0x80000000,    /* 1GB - 2GB */
        0x80000000,     0x100000000,   /* 2GB - 4GB */
        /* Very high memory for systems with lots of RAM */
        0x100000000,    0x200000000,   /* 4GB - 8GB */
        0x200000000,    0x400000000,   /* 8GB - 16GB */
    };
    
    int num_ranges = sizeof(scan_ranges) / sizeof(scan_ranges[0]) / 2;
    
    for (int range = 0; range < num_ranges; range++) {
        uint64_t start_addr = scan_ranges[range * 2];
        uint64_t end_addr = scan_ranges[range * 2 + 1];
        
        qemu_log("AdvDMA: Scanning physical memory range 0x%016" PRIx64 " - 0x%016" PRIx64 "\n", 
                 start_addr, end_addr);
        
        for (uint64_t addr = start_addr; addr < end_addr; addr += 0x1000) {
            uint16_t signature;
            if (!read_phys_mem(addr, &signature, 2)) {
                continue; /* Skip inaccessible memory */
            }
            
            if (signature == 0x5A4D) { /* MZ signature */
                /* Verify this is a valid PE header */
                uint32_t pe_offset;
                if (!read_phys_mem(addr + 0x3C, &pe_offset, 4) || pe_offset >= 0x1000) {
                    continue;
                }
                
                uint32_t pe_signature;
                if (!read_phys_mem(addr + pe_offset, &pe_signature, 4) || pe_signature != 0x00004550) {
                    continue; /* Not a valid PE */
                }
                
                /* Check if this looks like ntoskrnl.exe */
                /* Read IMAGE_FILE_HEADER to get characteristics */
                uint16_t characteristics;
                if (!read_phys_mem(addr + pe_offset + 0x16, &characteristics, 2)) {
                    continue;
                }
                
                /* Check for executable image characteristic */
                if (!(characteristics & 0x0002)) {
                    continue; /* Not executable */
                }
                
                /* Read IMAGE_OPTIONAL_HEADER to get more info */
                uint16_t optional_header_magic;
                if (!read_phys_mem(addr + pe_offset + 0x18, &optional_header_magic, 2)) {
                    continue;
                }
                
                /* Check for 64-bit PE (PE32+) */
                if (optional_header_magic != 0x020B) {
                    continue; /* Not PE32+ (64-bit) */
                }
                
                /* Read subsystem to verify it's a kernel image */
                uint16_t subsystem;
                if (!read_phys_mem(addr + pe_offset + 0x5C, &subsystem, 2)) {
                    continue;
                }
                
                /* Windows kernel has subsystem = 1 (native) */
                if (subsystem == 1) {
                    /* Additional verification: check for common kernel exports */
                    if (verify_kernel_image(addr, pe_offset)) {
                        s->kernel_base = addr;
                        s->kernel_info_cached = true;
                        qemu_log("AdvDMA: Found Windows kernel at physical address 0x%016" PRIx64 "\n", addr);
                        return addr;
                    }
                }
            }
        }
    }
    
    /* Method 2: Try scanning very low physical memory for bootloader artifacts */
    qemu_log("AdvDMA: Primary scan failed, trying low memory scan...\n");
    for (uint64_t addr = 0x100000; addr < 0x1000000; addr += 0x1000) {
        uint16_t signature;
        if (read_phys_mem(addr, &signature, 2) && signature == 0x5A4D) {
            uint32_t pe_offset;
            if (read_phys_mem(addr + 0x3C, &pe_offset, 4) && pe_offset < 0x1000) {
                uint32_t pe_signature;
                if (read_phys_mem(addr + pe_offset, &pe_signature, 4) && pe_signature == 0x00004550) {
                    if (verify_kernel_image(addr, pe_offset)) {
                        s->kernel_base = addr;
                        s->kernel_info_cached = true;
                        qemu_log("AdvDMA: Found Windows kernel in low memory at 0x%016" PRIx64 "\n", addr);
                        return addr;
                    }
                }
            }
        }
    }
    
    qemu_log("AdvDMA: Kernel base discovery failed after comprehensive scan\n");
    return 0;
}

/* Verify that a PE image is likely the Windows kernel */
static bool verify_kernel_image(uint64_t base_addr, uint32_t pe_offset) {
    /* Check for common kernel export names */
    const char* kernel_exports[] = {
        "NtCreateFile",
        "NtReadFile", 
        "NtWriteFile",
        "PsCreateSystemThread",
        "IoCreateDevice",
        "ZwOpenProcess",
        NULL
    };
    
    /* Read export directory RVA */
    uint32_t export_dir_rva;
    if (!read_phys_mem(base_addr + pe_offset + 0x88, &export_dir_rva, 4) || !export_dir_rva) {
        return false;
    }
    
    /* Read export directory */
    uint32_t number_of_names;
    uint32_t address_of_names;
    
    if (!read_phys_mem(base_addr + export_dir_rva + 0x18, &number_of_names, 4) || 
        !read_phys_mem(base_addr + export_dir_rva + 0x20, &address_of_names, 4)) {
        return false;
    }
    
    if (number_of_names == 0 || number_of_names > 10000) {
        return false; /* Sanity check */
    }
    
    /* Look for kernel-specific exports */
    int kernel_exports_found = 0;
    for (uint32_t i = 0; i < number_of_names && i < 1000; i++) {
        uint32_t name_rva;
        if (!read_phys_mem(base_addr + address_of_names + i * 4, &name_rva, 4)) {
            continue;
        }
        
        char export_name[32];
        if (!read_phys_mem(base_addr + name_rva, export_name, sizeof(export_name))) {
            continue;
        }
        export_name[31] = 0;
        
        /* Check against kernel export list */
        for (int j = 0; kernel_exports[j]; j++) {
            if (strncmp(export_name, kernel_exports[j], strlen(kernel_exports[j])) == 0) {
                kernel_exports_found++;
                if (kernel_exports_found >= 2) {
                    qemu_log("AdvDMA: Verified kernel image with exports: %s, etc.\n", export_name);
                    return true;
                }
            }
        }
    }
    
    return kernel_exports_found >= 1; /* At least one kernel export found */
}

/* Find PsActiveProcessHead using pattern scanning */
static uint64_t find_ps_active_process_head(AdvDMANICState *s) {
    if (s->kernel_info_cached && s->ps_active_process_head) {
        return s->ps_active_process_head;
    }
    
    uint64_t kernel_base = find_kernel_base(s);
    if (!kernel_base) {
        return 0;
    }
    
    /* Pattern scan for PsActiveProcessHead in the kernel */
    /* This is a simplified approach - in reality, you'd use more sophisticated patterns */
    for (uint64_t offset = 0; offset < 0x100000; offset += 8) {
        uint64_t candidate;
        if (read_phys_mem(kernel_base + offset, &candidate, 8)) {
            /* Check if this looks like a valid process list head */
            if (candidate >= 0xFFFF800000000000ULL && candidate < 0xFFFFF00000000000ULL) {
                /* Verify by checking if it points to valid EPROCESS structures */
                uint64_t first_process;
                if (read_phys_mem(candidate, &first_process, 8) && 
                    first_process >= 0xFFFF800000000000ULL) {
                    s->ps_active_process_head = candidate;
                    s->kernel_info_cached = true;
                    qemu_log("AdvDMA: Found PsActiveProcessHead at 0x%016" PRIx64 "\n", candidate);
                    return candidate;
                }
            }
        }
    }
    
    return 0;
}

/* Calculate confidence score for potential PsActiveProcessHead */
static uint32_t calculate_pshead_confidence(uint64_t list_head, uint64_t first_process) {
    uint32_t confidence = 30;  /* Base confidence for valid pointer range */
    
    /* Try to read the first EPROCESS structure */
    uint8_t eprocess_data[0x600];  /* Enough to read key EPROCESS fields */
    if (!read_phys_mem(first_process - EPROCESS_FLINK_OFFSET, eprocess_data, sizeof(eprocess_data))) {
        return 0;
    }
    
    /* Check for valid PID (should be non-zero and reasonable) */
    uint32_t pid = *(uint32_t*)(eprocess_data + EPROCESS_PID_OFFSET);
    if (pid > 0 && pid < 65536) {
        confidence += 20;
    }
    
    /* Check for valid CR3 (should be page-aligned) */
    uint64_t cr3 = *(uint64_t*)(eprocess_data + EPROCESS_CR3_OFFSET);
    if (cr3 != 0 && (cr3 & 0xFFF) == 0) {
        confidence += 20;
    }
    
    /* Check ImageFileName for printable characters */
    char *image_name = (char*)(eprocess_data + EPROCESS_NAME_OFFSET);
    bool has_printable = false;
    for (int i = 0; i < 15; i++) {
        if (image_name[i] >= 32 && image_name[i] < 127) {
            has_printable = true;
            break;
        }
    }
    if (has_printable) {
        confidence += 15;
    }
    
    /* Check if PEB pointer is in user space */
    uint64_t peb = *(uint64_t*)(eprocess_data + EPROCESS_PEB_OFFSET);
    if (peb > 0 && peb < 0x800000000000ULL) {  /* User space range */
        confidence += 15;
    }
    
    return (confidence > 100) ? 100 : confidence;
}

/* Enumerate all processes using EPROCESS traversal */
static int enumerate_processes(AdvDMANICState *s, ProcessInfo *processes, int max_count) {
    uint64_t ps_head = find_ps_active_process_head(s);
    if (!ps_head) {
        return 0;
    }
    
    int count = 0;
    uint64_t current_entry = ps_head;
    uint64_t first_process;
    
    if (!read_phys_mem(current_entry, &first_process, 8)) {
        return 0;
    }
    
    current_entry = first_process;
    
    do {
        if (count >= max_count) break;
        
        ProcessInfo *proc = &processes[count];
        memset(proc, 0, sizeof(ProcessInfo));
        
        /* Calculate EPROCESS address from FLINK */
        proc->eprocess = current_entry - EPROCESS_FLINK_OFFSET;
        
        /* Read PID */
        read_phys_mem(proc->eprocess + EPROCESS_PID_OFFSET, &proc->pid, 4);
        
        /* Read process name (ImageFileName) */
        read_phys_mem(proc->eprocess + EPROCESS_NAME_OFFSET, proc->name, 15);
        proc->name[15] = 0;
        
        /* Read CR3 (DirectoryTableBase) */
        read_phys_mem(proc->eprocess + EPROCESS_CR3_OFFSET, &proc->cr3, 8);
        
        /* Read PEB */
        read_phys_mem(proc->eprocess + EPROCESS_PEB_OFFSET, &proc->peb, 8);
        
        /* Read WoW64 PEB for 32-bit processes on 64-bit Windows */
        read_phys_mem(proc->eprocess + EPROCESS_WOW64_OFFSET, &proc->wow64_peb, 8);
        
        count++;
        
        /* Move to next process */
        if (!read_phys_mem(current_entry, &current_entry, 8)) {
            break;
        }
        
    } while (current_entry != first_process && current_entry != ps_head);
    
    return count;
}

/* Enumerate modules for a specific process */
static int enumerate_modules(AdvDMANICState *s, uint64_t cr3, uint64_t peb, bool is_wow64, 
                           ModuleInfo *modules, int max_count) {
    if (!peb) return 0;
    
    /* Read PEB_LDR_DATA pointer */
    uint64_t ldr_data;
    uint64_t ldr_phys = translate_virtual_address(cr3, peb + PEB_LDR_OFFSET);    if (!ldr_phys || !read_phys_mem(ldr_phys, &ldr_data, 8) || !ldr_data) {
        return 0;
    }
    
    /* Get InLoadOrderModuleList */
    uint64_t module_list_head = ldr_data + LDR_IN_LOAD_ORDER_OFFSET;
    uint64_t current_entry;
    
    uint64_t list_head_phys = translate_virtual_address(cr3, module_list_head);
    if (!list_head_phys || !read_phys_mem(list_head_phys, &current_entry, 8)) {
        return 0;
    }
    
    int count = 0;
    
    do {
        if (count >= max_count) break;
        
        /* Calculate LDR_DATA_TABLE_ENTRY address */
        uint64_t ldr_entry = current_entry - LDR_ENTRY_FLINK_OFFSET;
        
        ModuleInfo *mod = &modules[count];
        memset(mod, 0, sizeof(ModuleInfo));
        
        /* Read base address */
        uint64_t base_phys = translate_virtual_address(cr3, ldr_entry + LDR_ENTRY_BASE_OFFSET);
        if (base_phys) {
            read_phys_mem(base_phys, &mod->base_address, 8);
        }
        
        /* Read size of image */
        uint64_t size_phys = translate_virtual_address(cr3, ldr_entry + LDR_ENTRY_SIZE_OFFSET);
        if (size_phys) {
            read_phys_mem(size_phys, &mod->size_of_image, 4);
        }
        
        /* Read module name (UNICODE_STRING) */
        uint64_t name_string_phys = translate_virtual_address(cr3, ldr_entry + LDR_ENTRY_NAME_OFFSET);
        if (name_string_phys) {
            uint16_t name_length;
            uint64_t name_buffer;
            
            if (read_phys_mem(name_string_phys, &name_length, 2) && name_length > 0 && name_length < 512) {
                if (read_phys_mem(name_string_phys + 8, &name_buffer, 8) && name_buffer) {
                    uint64_t name_phys = translate_virtual_address(cr3, name_buffer);
                    if (name_phys) {
                        read_phys_mem(name_phys, mod->name, 
                                    (name_length < sizeof(mod->name)) ? name_length : sizeof(mod->name) - 2);
                    }
                }
            }
        }
        
        /* Calculate entry point from PE header */
        if (mod->base_address) {
            uint64_t pe_header_phys = translate_virtual_address(cr3, mod->base_address);
            if (pe_header_phys) {
                uint32_t pe_offset;
                if (read_phys_mem(pe_header_phys + 0x3C, &pe_offset, 4) && pe_offset < 0x1000) {
                    uint32_t entry_point_rva;
                    if (read_phys_mem(pe_header_phys + pe_offset + 0x28, &entry_point_rva, 4)) {
                        mod->entry_point = mod->base_address + entry_point_rva;
                    }
                }
            }
        }
        
        count++;
        
        /* Move to next module */
        uint64_t next_phys = translate_virtual_address(cr3, current_entry);
        if (!next_phys || !read_phys_mem(next_phys, &current_entry, 8)) {
            break;
        }
        
    } while (current_entry != module_list_head);
    
    return count;
}

/* Parse PE export table and find function address */
static uint64_t get_export_address(AdvDMANICState *s, uint64_t cr3, uint64_t module_base, 
                                 const char *function_name) {
    if (!module_base || !function_name) return 0;
    
    /* Read DOS header */
    uint64_t dos_header_phys = translate_virtual_address(cr3, module_base);
    if (!dos_header_phys) return 0;
    
    uint32_t pe_offset;
    if (!read_phys_mem(dos_header_phys + 0x3C, &pe_offset, 4) || pe_offset >= 0x1000) {
        return 0;
    }
    
    /* Read NT headers */
    uint64_t nt_headers_phys = translate_virtual_address(cr3, module_base + pe_offset);
    if (!nt_headers_phys) return 0;
    
    /* Get export directory RVA */
    uint32_t export_dir_rva;
    if (!read_phys_mem(nt_headers_phys + 0x88, &export_dir_rva, 4) || !export_dir_rva) {
        return 0;
    }
    
    /* Read export directory */
    uint64_t export_dir_phys = translate_virtual_address(cr3, module_base + export_dir_rva);
    if (!export_dir_phys) return 0;
    
    uint32_t number_of_names, number_of_functions;
    uint32_t address_of_functions, address_of_names, address_of_name_ordinals;
    
    read_phys_mem(export_dir_phys + 0x14, &number_of_functions, 4);
    read_phys_mem(export_dir_phys + 0x18, &number_of_names, 4);
    read_phys_mem(export_dir_phys + 0x1C, &address_of_functions, 4);
    read_phys_mem(export_dir_phys + 0x20, &address_of_names, 4);
    read_phys_mem(export_dir_phys + 0x24, &address_of_name_ordinals, 4);
    
    if (!number_of_names || !address_of_functions || !address_of_names || !address_of_name_ordinals) {
        return 0;
    }
    
    /* Search for function name */
    for (uint32_t i = 0; i < number_of_names; i++) {
        /* Get name RVA */
        uint64_t name_rva_phys = translate_virtual_address(cr3, module_base + address_of_names + i * 4);
        if (!name_rva_phys) continue;
        
        uint32_t name_rva;
        if (!read_phys_mem(name_rva_phys, &name_rva, 4)) continue;
        
        /* Read function name */
        uint64_t name_phys = translate_virtual_address(cr3, module_base + name_rva);
        if (!name_phys) continue;
        
        char export_name[64];
        if (!read_phys_mem(name_phys, export_name, sizeof(export_name))) continue;
        export_name[63] = 0;
        
        if (strcmp(export_name, function_name) == 0) {
            /* Get ordinal */
            uint64_t ordinal_phys = translate_virtual_address(cr3, module_base + address_of_name_ordinals + i * 2);
            if (!ordinal_phys) continue;
            
            uint16_t ordinal;
            if (!read_phys_mem(ordinal_phys, &ordinal, 2)) continue;
            
            /* Get function RVA */
            uint64_t func_rva_phys = translate_virtual_address(cr3, module_base + address_of_functions + ordinal * 4);
            if (!func_rva_phys) continue;
            
            uint32_t func_rva;
            if (!read_phys_mem(func_rva_phys, &func_rva, 4)) continue;
            
            return module_base + func_rva;
        }
    }
    
    return 0;
}

/* Handle client connection and process commands */
static void handle_client(AdvDMANICState *s, QIOChannelSocket *client) {
    AdvDMAPacket packet;
    Error *err = NULL;
    
    qemu_log("AdvDMA NIC: Client connected from socket\n");
    
    while (true) {
        // Clear packet structure
        memset(&packet, 0, sizeof(packet));
        
        // Receive packet header
        if (qio_channel_read_all(QIO_CHANNEL(client), (char *)&packet, sizeof(packet), &err) < 0 || err) {
            if (err) {
                qemu_log("AdvDMA NIC: Client read error: %s\n", error_get_pretty(err));
                error_free(err);
            } else {
                qemu_log("AdvDMA NIC: Client disconnected\n");
            }
            break;
        }
        
        // Validate magic number
        if (packet.magic != ADVDMA_MAGIC) {
            qemu_log("AdvDMA NIC: Invalid magic number: 0x%08X\n", packet.magic);
            continue;
        }
        
        qemu_log("AdvDMA NIC: Received command 0x%04X\n", packet.command);
        
        // Prepare response
        AdvDMAPacket response = {
            .magic = ADVDMA_MAGIC,
            .command = packet.command,
            .status = 1,  // Default to error
            .data_size = 0,
            .param1 = 0,
            .param2 = 0,
            .param3 = 0
        };
        
        // Process commands
        switch (packet.command) {
        case CMD_PING:
            qemu_log("AdvDMA NIC: Processing PING command\n");
            response.status = 0;            response.param1 = 0x504F4E47; // 'PONG'
            break;
            
        case CMD_GET_KERNEL_BASE: {
            qemu_log("AdvDMA NIC: Processing GET_KERNEL_BASE command\n");
            uint64_t kernel_base = find_kernel_base(s);
            if (kernel_base) {
                response.status = 0;
                response.param1 = kernel_base;
                qemu_log("AdvDMA NIC: Found kernel base: 0x%016" PRIx64 "\n", kernel_base);
            } else {
                qemu_log("AdvDMA NIC: Failed to find kernel base\n");
                response.status = 1;
            }
            break;
        }
        
        case CMD_READ_PHYS: {
            uint64_t address = packet.param1;
            uint32_t size = (uint32_t)packet.param2;
            qemu_log("AdvDMA NIC: Processing READ_PHYS: addr=0x%016" PRIx64 ", size=0x%X\n", address, size);
            
            if (size > 0 && size <= MAX_TRANSFER_SIZE) {
                uint8_t *buffer = g_malloc(size);
                if (read_phys_mem(address, buffer, size)) {
                    response.status = 0;
                    response.data_size = size;
                    
                    // Send response header
                    qio_channel_write_all(QIO_CHANNEL(client), (char *)&response, sizeof(response), &err);
                    // Send data
                    qio_channel_write_all(QIO_CHANNEL(client), (char *)buffer, size, &err);
                    g_free(buffer);
                    continue; // Skip sending response again
                } else {
                    qemu_log("AdvDMA NIC: Physical memory read failed\n");
                    response.status = 1;
                }
                g_free(buffer);
            } else {
                qemu_log("AdvDMA NIC: Invalid read size: 0x%X\n", size);
                response.status = 2; // Invalid size
            }
            break;
        }
        
        case CMD_WRITE_PHYS: {
            uint64_t address = packet.param1;
            uint32_t size = (uint32_t)packet.param2;
            qemu_log("AdvDMA NIC: Processing WRITE_PHYS: addr=0x%016" PRIx64 ", size=0x%X\n", address, size);
            
            if (size > 0 && size <= MAX_TRANSFER_SIZE && packet.data_size == size) {
                uint8_t *buffer = g_malloc(size);
                if (qio_channel_read_all(QIO_CHANNEL(client), (char *)buffer, size, &err) >= 0) {
                    if (write_phys_mem(address, buffer, size)) {
                        response.status = 0;
                        qemu_log("AdvDMA NIC: Physical memory write successful\n");
                    } else {
                        qemu_log("AdvDMA NIC: Physical memory write failed\n");
                        response.status = 1;
                    }
                } else {
                    qemu_log("AdvDMA NIC: Failed to read write data from client\n");
                    response.status = 3;
                }
                g_free(buffer);
            } else {
                qemu_log("AdvDMA NIC: Invalid write parameters\n");
                response.status = 2;
            }
            break;
        }
        
        case CMD_READ_VIRT: {
            uint64_t va = packet.param1;
            uint32_t size = (uint32_t)packet.param2;
            uint64_t cr3 = packet.param3;
            qemu_log("AdvDMA NIC: Processing READ_VIRT: va=0x%016" PRIx64 ", size=0x%X, cr3=0x%016" PRIx64 "\n", va, size, cr3);
            
            if (size > 0 && size <= MAX_TRANSFER_SIZE) {
                uint64_t phys_addr = translate_virtual_address(cr3, va);
                if (phys_addr) {
                    uint8_t *buffer = g_malloc(size);
                    if (read_phys_mem(phys_addr, buffer, size)) {
                        response.status = 0;
                        response.data_size = size;
                        
                        // Send response header
                        qio_channel_write_all(QIO_CHANNEL(client), (char *)&response, sizeof(response), &err);
                        // Send data
                        qio_channel_write_all(QIO_CHANNEL(client), (char *)buffer, size, &err);
                        g_free(buffer);
                        continue;
                    } else {
                        qemu_log("AdvDMA NIC: Virtual memory read failed (physical read)\n");
                        response.status = 1;
                    }
                    g_free(buffer);
                } else {
                    qemu_log("AdvDMA NIC: Virtual address translation failed\n");
                    response.status = 4; // Translation failed
                }
            } else {
                qemu_log("AdvDMA NIC: Invalid virtual read size\n");
                response.status = 2;
            }
            break;
        }
        
        case CMD_WRITE_VIRT: {
            uint64_t va = packet.param1;
            uint32_t size = (uint32_t)packet.param2;
            uint64_t cr3 = packet.param3;
            qemu_log("AdvDMA NIC: Processing WRITE_VIRT: va=0x%016" PRIx64 ", size=0x%X, cr3=0x%016" PRIx64 "\n", va, size, cr3);
            
            if (size > 0 && size <= MAX_TRANSFER_SIZE && packet.data_size == size) {
                uint64_t phys_addr = translate_virtual_address(cr3, va);
                if (phys_addr) {
                    uint8_t *buffer = g_malloc(size);
                    if (qio_channel_read_all(QIO_CHANNEL(client), (char *)buffer, size, &err) >= 0) {
                        if (write_phys_mem(phys_addr, buffer, size)) {
                            response.status = 0;
                            qemu_log("AdvDMA NIC: Virtual memory write successful\n");
                        } else {
                            qemu_log("AdvDMA NIC: Virtual memory write failed\n");
                            response.status = 1;
                        }
                    } else {
                        qemu_log("AdvDMA NIC: Failed to read write data\n");
                        response.status = 3;
                    }
                    g_free(buffer);
                } else {
                    qemu_log("AdvDMA NIC: Virtual address translation failed\n");
                    response.status = 4;
                }
            } else {
                qemu_log("AdvDMA NIC: Invalid virtual write parameters\n");
                response.status = 2;
            }
            break;
        }
        
        case CMD_TRANSLATE_VA: {
            uint64_t va = packet.param1;
            uint64_t cr3 = packet.param2;
            qemu_log("AdvDMA NIC: Processing TRANSLATE_VA: va=0x%016" PRIx64 ", cr3=0x%016" PRIx64 "\n", va, cr3);
            
            uint64_t phys_addr = translate_virtual_address(cr3, va);
            if (phys_addr) {
                response.status = 0;
                response.param1 = phys_addr;
                qemu_log("AdvDMA NIC: Translation successful: 0x%016" PRIx64 " -> 0x%016" PRIx64 "\n", va, phys_addr);
            } else {
                qemu_log("AdvDMA NIC: Translation failed\n");
                response.status = 1;
            }
            break;
        }        case CMD_ENUM_PROCESSES: {
            qemu_log("AdvDMA NIC: Processing ENUM_PROCESSES command\n");
            ProcessInfo *processes = g_malloc(sizeof(ProcessInfo) * MAX_PROCESSES);
            int count = enumerate_processes(s, processes, MAX_PROCESSES);
            if (count > 0) {
                response.status = 0;
                response.data_size = count * sizeof(ProcessInfo);
                response.param1 = count;
                qio_channel_write_all(QIO_CHANNEL(client), (char *)&response, sizeof(response), &err);
                qio_channel_write_all(QIO_CHANNEL(client), (char *)processes, response.data_size, &err);
                g_free(processes);
                continue;
            } else {
                response.status = 1;
            }
            g_free(processes);
            break;
        }        case CMD_ENUM_MODULES: {
            uint64_t cr3 = packet.param1;
            uint64_t peb = packet.param2;
            bool is_wow64 = (packet.param3 != 0);
            qemu_log("AdvDMA NIC: Processing ENUM_MODULES: cr3=0x%016" PRIx64 ", peb=0x%016" PRIx64 ", wow64=%d\n", cr3, peb, is_wow64);
            
            ModuleInfo *modules = g_malloc(sizeof(ModuleInfo) * MAX_MODULES);
            int count = enumerate_modules(s, cr3, peb, is_wow64, modules, MAX_MODULES);
            if (count > 0) {
                response.status = 0;
                response.data_size = count * sizeof(ModuleInfo);
                response.param1 = count;
                qio_channel_write_all(QIO_CHANNEL(client), (char *)&response, sizeof(response), &err);
                qio_channel_write_all(QIO_CHANNEL(client), (char *)modules, response.data_size, &err);
                g_free(modules);
                continue;
            } else {
                response.status = 1;
            }
            g_free(modules);
            break;
        }        case CMD_GET_EXPORT: {
            uint64_t cr3 = packet.param1;
            uint64_t module_base = packet.param2;
            qemu_log("AdvDMA NIC: Processing GET_EXPORT: cr3=0x%016" PRIx64 ", module=0x%016" PRIx64 ", size=%u\n", 
                     cr3, module_base, packet.data_size);
            
            if (packet.data_size > 0 && packet.data_size < 256) {
                char *function_name = g_malloc(packet.data_size + 1);
                if (qio_channel_read_all(QIO_CHANNEL(client), function_name, packet.data_size, &err) >= 0) {
                    function_name[packet.data_size] = 0;
                    qemu_log("AdvDMA NIC: Looking for export: %s\n", function_name);
                    
                    uint64_t func_addr = get_export_address(s, cr3, module_base, function_name);
                    if (func_addr) {
                        response.status = 0;
                        response.param1 = func_addr;
                        qemu_log("AdvDMA NIC: Export found at: 0x%016" PRIx64 "\n", func_addr);
                    } else {
                        response.status = 1;
                        qemu_log("AdvDMA NIC: Export not found\n");
                    }
                } else {
                    qemu_log("AdvDMA NIC: Failed to read function name\n");
                    response.status = 3;
                }
                g_free(function_name);
            } else {
                qemu_log("AdvDMA NIC: Invalid function name size: %u\n", packet.data_size);
                response.status = 2;
            }
            break;
        }        case CMD_BULK_SCAN: {
            qemu_log("AdvDMA NIC: Processing BULK_SCAN command\n");
            uint64_t address = packet.param1;
            uint32_t size = (uint32_t)packet.param2;
            uint32_t max_results = (uint32_t)packet.param3;
            
            // Special case: PsActiveProcessHead fast scan (param1 = 0, param2 = 0)
            if (address == 0 && size == 0) {
                qemu_log("AdvDMA NIC: Performing multi-threaded PsActiveProcessHead scan\n");
                BulkScanResult *results = g_malloc(sizeof(BulkScanResult) * max_results);
                uint32_t found_count = fast_pshead_scan_mt(s, results, max_results);
                
                if (found_count > 0) {
                    response.status = 0;
                    response.param1 = found_count;
                    response.data_size = found_count * sizeof(BulkScanResult);
                    
                    // Send response header
                    qio_channel_write_all(QIO_CHANNEL(client), (char *)&response, sizeof(response), &err);
                    // Send results
                    qio_channel_write_all(QIO_CHANNEL(client), (char *)results, response.data_size, &err);
                    g_free(results);
                    continue; // Skip sending response again
                } else {
                    qemu_log("AdvDMA NIC: Multi-threaded scan found no candidates\n");
                    response.status = 1;
                }
                g_free(results);
            }
            // Regular bulk memory read
            else if (size > 0 && size <= MAX_TRANSFER_SIZE && max_results > 0 && max_results <= MAX_SCAN_RESULTS) {
                uint8_t *buffer = g_malloc(size);
                if (read_phys_mem(address, buffer, size)) {
                    response.status = 0;
                    response.data_size = size;
                    
                    // Send response header
                    qio_channel_write_all(QIO_CHANNEL(client), (char *)&response, sizeof(response), &err);
                    // Send data
                    qio_channel_write_all(QIO_CHANNEL(client), (char *)buffer, size, &err);
                    g_free(buffer);
                    continue; // Skip sending response again
                } else {
                    qemu_log("AdvDMA NIC: Bulk memory read failed\n");
                    response.status = 1;
                }
                g_free(buffer);
            } else {
                qemu_log("AdvDMA NIC: Invalid bulk scan parameters\n");
                response.status = 2;
            }
            break;
        }case CMD_PATTERN_SCAN: {
            qemu_log("AdvDMA NIC: Processing PATTERN_SCAN command\n");
            uint64_t start_addr = packet.param1;
            uint64_t end_addr = packet.param2;
            uint32_t pattern_size = (uint32_t)packet.param3;
            
            if (pattern_size > 0 && pattern_size <= sizeof(PatternScanRequest) - 24) {
                uint8_t *pattern_buffer = g_malloc(pattern_size);
                if (qio_channel_read_all(QIO_CHANNEL(client), (char *)pattern_buffer, pattern_size, &err) >= 0) {
                    // Perform the pattern scan in chunks
                    uint64_t addr = start_addr;
                    uint32_t chunk_size = BULK_SCAN_CHUNK_SIZE;
                    uint32_t total_found = 0;
                    
                    while (addr < end_addr && total_found < MAX_SCAN_RESULTS) {
                        if (addr + chunk_size > end_addr) {
                            chunk_size = end_addr - addr;
                        }
                        
                        uint8_t *scan_buffer = g_malloc(chunk_size);
                        if (read_phys_mem(addr, scan_buffer, chunk_size)) {
                            // Scan the chunk for the pattern
                            for (uint32_t offset = 0; offset <= chunk_size - pattern_size; offset++) {
                                if (memcmp(scan_buffer + offset, pattern_buffer, pattern_size) == 0) {
                                    // Pattern found
                                    uint64_t found_addr = addr + offset;
                                    
                                    // Send result immediately
                                    BulkScanResult result = {
                                        .address = found_addr,
                                        .value = 0,  // Value not applicable for pattern scan
                                        .confidence = 100,  // High confidence for exact match
                                        .reserved = 0
                                    };
                                    qio_channel_write_all(QIO_CHANNEL(client), (char *)&result, sizeof(result), &err);
                                    total_found++;
                                    
                                    if (total_found >= MAX_SCAN_RESULTS) {
                                        break;
                                    }
                                }
                            }
                        } else {
                            qemu_log("AdvDMA NIC: Pattern scan read failed at 0x%016" PRIx64 "\n", addr);
                        }
                        g_free(scan_buffer);
                        addr += chunk_size;
                    }
                    
                    response.status = 0;
                    response.param1 = total_found;
                } else {
                    qemu_log("AdvDMA NIC: Failed to read pattern data\n");
                    response.status = 3;
                }
                g_free(pattern_buffer);
            } else {
                qemu_log("AdvDMA NIC: Invalid pattern scan parameters\n");
                response.status = 2;
            }
            break;
        }
        
        default:
            qemu_log("AdvDMA NIC: Unknown command: 0x%04X\n", packet.command);
            response.status = 0xFF; // Unknown command
            break;
        }
        
        // Send response for commands that don't send data
        qio_channel_write_all(QIO_CHANNEL(client), (char *)&response, sizeof(response), &err);
        if (err) {
            qemu_log("AdvDMA NIC: Failed to send response: %s\n", error_get_pretty(err));
            error_free(err);
            break;
        }    }
    
    object_unref(OBJECT(client));
    qemu_log("AdvDMA NIC: Client handler finished\n");
}

/* Network thread function */
static void *network_thread(void *opaque) {
    AdvDMANICState *s = (AdvDMANICState *)opaque;
    SocketAddress addr = { .type = SOCKET_ADDRESS_TYPE_INET };
    addr.u.inet.host = g_strdup("0.0.0.0");
    addr.u.inet.port = g_strdup_printf("%d", s->port);
    
    QIOChannelSocket *listener = qio_channel_socket_new();
    Error *err = NULL;
    
    qemu_log("AdvDMA NIC: Starting network thread on port %d\n", s->port);
    
    if (qio_channel_socket_listen_sync(listener, &addr, 1, &err) < 0) {
        error_report("AdvDMA NIC: Failed to listen on 0.0.0.0:%d", s->port);
        if (err) {
            error_report("AdvDMA NIC: Listen error: %s", error_get_pretty(err));
            error_free(err);
        }
        g_free(addr.u.inet.host);
        g_free(addr.u.inet.port);
        object_unref(OBJECT(listener));
        return NULL;
    }
    
    error_report("AdvDMA NIC: Successfully listening on 0.0.0.0:%d", s->port);
    qemu_log("AdvDMA NIC: Ready to accept connections\n");
    
    while (s->running) {
        QIOChannelSocket *client = qio_channel_socket_accept(listener, &err);
        if (!client) {
            if (err) {
                if (s->running) { // Only log if we're supposed to be running
                    qemu_log("AdvDMA NIC: Accept error: %s\n", error_get_pretty(err));
                }
                error_free(err);
                err = NULL;
            }
            // Small delay to prevent busy waiting
            g_usleep(10000); // 10ms            continue;
        }
        
        qemu_log("AdvDMA NIC: New client connection accepted\n");
        handle_client(s, client);
    }
    
    object_unref(OBJECT(listener));
    g_free(addr.u.inet.host);
    g_free(addr.u.inet.port);
    
    qemu_log("AdvDMA NIC: Network thread shutting down\n");
    return NULL;
}

/* Device initialization */
static void advdma_nic_realize(PCIDevice *pci_dev, Error **errp) {
    AdvDMANICState *s = ADVDMA_NIC(pci_dev);
    
    /* Initialize MMIO region (required for proper PCI device) */
    memory_region_init_io(&s->mmio, OBJECT(s), NULL, s, "advdma-mmio", 0x1000);
    pci_register_bar(pci_dev, 0, PCI_BASE_ADDRESS_SPACE_MEMORY, &s->mmio);
    
    /* Initialize state */
    s->port = ADVDMA_DEFAULT_PORT;
    s->running = true;
    s->kernel_info_cached = false;
    s->kernel_base = 0;
    s->ps_active_process_head = 0;
      qemu_mutex_init(&s->lock);
    
    /* Initialize thread pool for multi-threaded scanning */
    init_scan_thread_pool(s);
    
    /* Start network thread */
    qemu_thread_create(&s->thread, "advdma-net", network_thread, s, QEMU_THREAD_JOINABLE);
    
    qemu_log("AdvDMA NIC: Device initialized on port %d\n", s->port);
}

/* Device cleanup */
static void advdma_nic_exit(PCIDevice *pci_dev) {
    AdvDMANICState *s = ADVDMA_NIC(pci_dev);
    
    s->running = false;
    
    /* Shutdown thread pool first */
    shutdown_scan_thread_pool(s);
    
    /* Wait for network thread */
    qemu_thread_join(&s->thread);
    qemu_mutex_destroy(&s->lock);
    
    qemu_log("AdvDMA NIC: Device cleaned up\n");
}

/* Device class initialization */
static void advdma_nic_class_init(ObjectClass *klass, const void *data) {
    DeviceClass *dc = DEVICE_CLASS(klass);
    PCIDeviceClass *k = PCI_DEVICE_CLASS(klass);
    
    k->realize = advdma_nic_realize;
    k->exit = advdma_nic_exit;
    k->vendor_id = PCI_VENDOR_ID_INTEL;
    k->device_id = PCI_DEVICE_ID_82574L;
    k->revision = 0x00;
    k->class_id = PCI_CLASS_NETWORK_ETHERNET;
    
    dc->desc = "Advanced DMA Intel 82574L Gigabit Ethernet Controller";
    set_bit(DEVICE_CATEGORY_NETWORK, dc->categories);
}

/* Type information */
static const TypeInfo advdma_nic_info = {
    .name = TYPE_ADVDMA_NIC,
    .parent = TYPE_PCI_DEVICE,
    .instance_size = sizeof(AdvDMANICState),
    .class_init = advdma_nic_class_init,
    .interfaces = (InterfaceInfo[]) {
        { INTERFACE_CONVENTIONAL_PCI_DEVICE },
        { },
    },
};

/* Register device type */
static void advdma_nic_register_types(void) {
    type_register_static(&advdma_nic_info);
}

type_init(advdma_nic_register_types)

/* ================= MULTI-THREADED SCANNING IMPLEMENTATION ================= */

/* Thread pool worker function */
static void *scan_worker_thread(void *opaque) {
    AdvDMANICState *s = (AdvDMANICState *)opaque;
    ScanThreadPool *pool = &s->scan_pool;
    
    qemu_log("AdvDMA: Scan worker thread started\n");
    
    while (pool->pool_running) {
        ScanWorkItem *work = NULL;
        
        // Wait for work
        qemu_mutex_lock(&pool->queue_mutex);
        while (QTAILQ_EMPTY(&pool->work_queue) && pool->pool_running) {
            qemu_cond_wait(&pool->work_available, &pool->queue_mutex);
        }
        
        if (!pool->pool_running) {
            qemu_mutex_unlock(&pool->queue_mutex);
            break;
        }
        
        // Get work item
        work = QTAILQ_FIRST(&pool->work_queue);
        if (work) {
            QTAILQ_REMOVE(&pool->work_queue, work, next);
            pool->active_workers++;
        }
        qemu_mutex_unlock(&pool->queue_mutex);
        
        if (!work) continue;
        
        // Process the work item
        uint64_t addr = work->start_addr;
        uint64_t end_addr = work->end_addr;
        uint32_t chunk_size = work->chunk_size;
        
        qemu_log("AdvDMA: Worker scanning 0x%016" PRIx64 " - 0x%016" PRIx64 "\n", addr, end_addr);
        
        while (addr < end_addr && !(*work->scan_complete)) {
            uint64_t current_chunk_size = (end_addr - addr > chunk_size) ? chunk_size : (end_addr - addr);
            uint8_t *chunk_data = g_malloc(current_chunk_size);
            
            if (read_phys_mem(addr, chunk_data, current_chunk_size)) {
                // Scan the chunk for potential pointers
                for (uint64_t offset = 0; offset < current_chunk_size - 8; offset += 8) {
                    uint64_t *potential_ptr = (uint64_t*)(chunk_data + offset);
                    uint64_t value = *potential_ptr;
                    
                    // Check if this looks like a kernel pointer
                    if (value >= 0xFFFF800000000000ULL && value < 0xFFFFF00000000000ULL) {
                        // This could be PsActiveProcessHead - verify it points to process structures
                        uint64_t first_process;
                        if (read_phys_mem(value, (uint8_t*)&first_process, 8)) {
                            if (first_process >= 0xFFFF800000000000ULL && first_process < 0xFFFFF00000000000ULL) {
                                // Calculate confidence
                                uint32_t confidence = calculate_pshead_confidence(value, first_process);
                                
                                if (confidence >= 50) {  // Minimum confidence threshold
                                    qemu_mutex_lock(work->results_mutex);
                                    
                                    if (*work->result_count < work->max_results) {
                                        work->results[*work->result_count].address = addr + offset;
                                        work->results[*work->result_count].value = value;
                                        work->results[*work->result_count].confidence = confidence;
                                        (*work->result_count)++;
                                        
                                        qemu_log("AdvDMA: Worker found candidate at 0x%016" PRIx64 " -> 0x%016" PRIx64 " (confidence: %u%%)\n",
                                                 addr + offset, value, confidence);
                                    }
                                    
                                    qemu_mutex_unlock(work->results_mutex);
                                }
                            }
                        }
                    }
                }
            }
            
            g_free(chunk_data);
            addr += current_chunk_size;
        }
        
        // Mark work as complete
        qemu_mutex_lock(&pool->queue_mutex);
        pool->active_workers--;
        g_free(work);
        
        // Signal completion if no more work
        if (QTAILQ_EMPTY(&pool->work_queue) && pool->active_workers == 0) {
            qemu_cond_broadcast(&pool->work_complete);
        }
        qemu_mutex_unlock(&pool->queue_mutex);
    }
    
    qemu_log("AdvDMA: Scan worker thread finished\n");
    return NULL;
}

/* Initialize thread pool */
static void init_scan_thread_pool(AdvDMANICState *s) {
    ScanThreadPool *pool = &s->scan_pool;
    
    qemu_mutex_init(&pool->queue_mutex);
    qemu_cond_init(&pool->work_available);
    qemu_cond_init(&pool->work_complete);
    QTAILQ_INIT(&pool->work_queue);
    
    pool->pool_running = true;
    pool->active_workers = 0;
    pool->total_workers = SCAN_THREAD_POOL_SIZE;
    
    // Create worker threads
    for (int i = 0; i < SCAN_THREAD_POOL_SIZE; i++) {
        char thread_name[32];
        snprintf(thread_name, sizeof(thread_name), "advdma-scan%d", i);
        qemu_thread_create(&pool->threads[i], thread_name, scan_worker_thread, s, QEMU_THREAD_JOINABLE);
    }
    
    qemu_log("AdvDMA: Initialized scan thread pool with %d threads\n", SCAN_THREAD_POOL_SIZE);
}

/* Shutdown thread pool */
static void shutdown_scan_thread_pool(AdvDMANICState *s) {
    ScanThreadPool *pool = &s->scan_pool;
    
    qemu_log("AdvDMA: Shutting down scan thread pool\n");
    
    // Signal shutdown
    qemu_mutex_lock(&pool->queue_mutex);
    pool->pool_running = false;
    qemu_cond_broadcast(&pool->work_available);
    qemu_mutex_unlock(&pool->queue_mutex);
    
    // Wait for all threads to finish
    for (int i = 0; i < SCAN_THREAD_POOL_SIZE; i++) {
        qemu_thread_join(&pool->threads[i]);
    }
    
    // Clean up any remaining work
    ScanWorkItem *work, *next_work;
    QTAILQ_FOREACH_SAFE(work, &pool->work_queue, next, next_work) {
        QTAILQ_REMOVE(&pool->work_queue, work, next);
        g_free(work);
    }
    
    qemu_cond_destroy(&pool->work_available);
    qemu_cond_destroy(&pool->work_complete);
    qemu_mutex_destroy(&pool->queue_mutex);
    
    qemu_log("AdvDMA: Scan thread pool shutdown complete\n");
}

/* Multi-threaded PsActiveProcessHead scan */
static uint32_t fast_pshead_scan_mt(AdvDMANICState *s, BulkScanResult *results, uint32_t max_results) {
    // Get kernel base first for adaptive scanning
    uint64_t kernel_base = find_kernel_base(s);
    if (!kernel_base) {
        qemu_log("AdvDMA: Could not find kernel base for adaptive scanning\n");
        kernel_base = 0x0000000003800000; // Use default/common base
    }
    
    qemu_log("AdvDMA: Using kernel base 0x%016" PRIx64 " for adaptive multi-threaded scan\n", kernel_base);
    
    // Adaptive scan ranges based on actual kernel base
    // These ranges are relative to kernel base and cover typical PsActiveProcessHead locations
    uint64_t relative_ranges[][2] = {
        // .data section - where global variables are typically stored
        {0x480000, 0x500000},   // 512KB range in .data
        {0x500000, 0x580000},   // Extended .data range
        {0x580000, 0x600000},   // High .data range
        {0x600000, 0x680000},   // Very high .data range
        
        // .rdata section - read-only data that might contain pointers
        {0x10000, 0x90000},     // 512KB in .rdata
        {0x90000, 0x120000},    // Extended .rdata
        
        // Additional common locations
        {0x700000, 0x800000},   // 1MB range
        {0x800000, 0x900000},   // 1MB range
        {0x900000, 0xA00000},   // 1MB range
        {0xA00000, 0xB00000},   // 1MB range
    };
    
    // Also scan some absolute ranges that are commonly used regardless of kernel base
    uint64_t absolute_ranges[][2] = {
        {0x0000000003C80000, 0x0000000003D00000},  // Common kernel data area 1
        {0x0000000003D00000, 0x0000000003D80000},  // Common kernel data area 2
        {0x0000000003E00000, 0x0000000003E80000},  // Alternative location 1
        {0x0000000003F00000, 0x0000000003F80000},  // Alternative location 2
        {0x0000000004000000, 0x0000000004080000},  // Higher memory region 1
        {0x0000000004100000, 0x0000000004180000},  // Higher memory region 2
        {0x0000000004200000, 0x0000000004280000},  // Extended search 1
        {0x0000000004300000, 0x0000000004380000},  // Extended search 2
        {0x0000000004400000, 0x0000000004480000},  // Extended search 3
        {0x0000000004500000, 0x0000000004580000},  // Extended search 4
        {0x0000000005000000, 0x0000000005080000},  // High memory region 1
        {0x0000000005100000, 0x0000000005180000},  // High memory region 2
    };
    
    uint32_t found_count = 0;
    ScanThreadPool *pool = &s->scan_pool;
    
    QemuMutex results_mutex;
    qemu_mutex_init(&results_mutex);
    bool scan_complete = false;
    
    int total_ranges = sizeof(relative_ranges) / sizeof(relative_ranges[0]) + 
                      sizeof(absolute_ranges) / sizeof(absolute_ranges[0]);
    
    qemu_log("AdvDMA: Multi-threaded PsActiveProcessHead scan starting with %d ranges (kernel-relative + absolute)\n", total_ranges);
    
    // Distribute work across thread pool
    qemu_mutex_lock(&pool->queue_mutex);
    
    // Add kernel-relative ranges
    for (int i = 0; i < sizeof(relative_ranges) / sizeof(relative_ranges[0]); i++) {
        uint64_t start_addr = kernel_base + relative_ranges[i][0];
        uint64_t end_addr = kernel_base + relative_ranges[i][1];
        
        // Split large ranges into smaller work items for better load balancing
        uint64_t range_size = end_addr - start_addr;
        uint64_t work_chunk_size = range_size / (SCAN_THREAD_POOL_SIZE * 2); // Create 2x work items per thread
        if (work_chunk_size < BULK_SCAN_CHUNK_SIZE) {
            work_chunk_size = range_size; // Don't split if too small
        }
        
        for (uint64_t addr = start_addr; addr < end_addr; addr += work_chunk_size) {
            uint64_t work_end = addr + work_chunk_size;
            if (work_end > end_addr) work_end = end_addr;
            
            ScanWorkItem *work = g_malloc0(sizeof(ScanWorkItem));
            work->start_addr = addr;
            work->end_addr = work_end;
            work->chunk_size = BULK_SCAN_CHUNK_SIZE;
            work->results = results;
            work->result_count = &found_count;
            work->max_results = max_results;
            work->scan_complete = &scan_complete;
            work->results_mutex = &results_mutex;
            
            QTAILQ_INSERT_TAIL(&pool->work_queue, work, next);
        }
    }
    
    // Add absolute ranges
    for (int i = 0; i < sizeof(absolute_ranges) / sizeof(absolute_ranges[0]); i++) {
        uint64_t start_addr = absolute_ranges[i][0];
        uint64_t end_addr = absolute_ranges[i][1];
        
        // Split large ranges into smaller work items for better load balancing
        uint64_t range_size = end_addr - start_addr;
        uint64_t work_chunk_size = range_size / (SCAN_THREAD_POOL_SIZE * 2); // Create 2x work items per thread
        if (work_chunk_size < BULK_SCAN_CHUNK_SIZE) {
            work_chunk_size = range_size; // Don't split if too small
        }
        
        for (uint64_t addr = start_addr; addr < end_addr; addr += work_chunk_size) {
            uint64_t work_end = addr + work_chunk_size;
            if (work_end > end_addr) work_end = end_addr;
            
            ScanWorkItem *work = g_malloc0(sizeof(ScanWorkItem));
            work->start_addr = addr;
            work->end_addr = work_end;
            work->chunk_size = BULK_SCAN_CHUNK_SIZE;
            work->results = results;
            work->result_count = &found_count;
            work->max_results = max_results;
            work->scan_complete = &scan_complete;
            work->results_mutex = &results_mutex;
            
            QTAILQ_INSERT_TAIL(&pool->work_queue, work, next);
        }
    }
    
    // Wake up all workers
    qemu_cond_broadcast(&pool->work_available);
    
    // Wait for all work to complete
    while (!QTAILQ_EMPTY(&pool->work_queue) || pool->active_workers > 0) {
        qemu_cond_wait(&pool->work_complete, &pool->queue_mutex);
    }
    
    qemu_mutex_unlock(&pool->queue_mutex);
    qemu_mutex_destroy(&results_mutex);
    
    qemu_log("AdvDMA: Multi-threaded scan completed, found %d candidates\n", found_count);
    return found_count;
}

/* ================= END MULTI-THREADED SCANNING ================= */

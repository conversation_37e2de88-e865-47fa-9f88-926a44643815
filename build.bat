@echo off
REM Build script for DMA Memory Analyzer
REM Requires Visual Studio 2019 or later

echo Building DMA Memory Analyzer...

REM Check if MSBuild is available
where msbuild >nul 2>nul
if %ERRORLEVEL% neq 0 (
    echo Error: MSBuild not found. Please run from Visual Studio Developer Command Prompt.
    pause
    exit /b 1
)

REM Create output directories
if not exist "bin" mkdir bin
if not exist "bin\x64" mkdir bin\x64
if not exist "bin\x64\Debug" mkdir bin\x64\Debug
if not exist "bin\x64\Release" mkdir bin\x64\Release
if not exist "obj" mkdir obj

echo Building Debug configuration...
msbuild DMAMemoryAnalyzer.sln /p:Configuration=Debug /p:Platform=x64 /m
if %ERRORLEVEL% neq 0 (
    echo Debug build failed!
    pause
    exit /b 1
)

echo Building Release configuration...
msbuild DMAMemoryAnalyzer.sln /p:Configuration=Release /p:Platform=x64 /m
if %ERRORLEVEL% neq 0 (
    echo Release build failed!
    pause
    exit /b 1
)

echo.
echo Build completed successfully!
echo.
echo Debug executable:   bin\x64\Debug\DMAMemoryAnalyzer.exe
echo Release executable: bin\x64\Release\DMAMemoryAnalyzer.exe
echo.
echo Usage examples:
echo   bin\x64\Release\DMAMemoryAnalyzer.exe --help
echo   bin\x64\Release\DMAMemoryAnalyzer.exe --host ************* --test-connection
echo   bin\x64\Release\DMAMemoryAnalyzer.exe --host ************* --dump memory.raw
echo.
pause

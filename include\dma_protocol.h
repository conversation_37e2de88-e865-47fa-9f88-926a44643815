#pragma once
/*
 * DMA Protocol Definitions
 * Compatible with QEMU Streamlined DMA Device
 */

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Protocol Constants */
#define DMA_MAGIC                   0x444D4100  /* 'DMA\0' */
#define DMA_DEFAULT_PORT            31338

/* Command definitions */
#define CMD_PING                    0x1000
#define CMD_READ_PHYS               0x2000
#define CMD_WRITE_PHYS              0x2001
#define CMD_TRANSLATE_VA            0x2002
#define CMD_DUMP_MEMORY             0x3000
#define CMD_GET_MEMORY_LAYOUT       0x3001

/* Status codes */
#define STATUS_SUCCESS              0x0000
#define STATUS_ERROR                0x0001
#define STATUS_INVALID_SIZE         0x0002
#define STATUS_READ_FAILED          0x0003
#define STATUS_WRITE_FAILED         0x0004
#define STATUS_TRANSLATE_FAILED     0x0005
#define STATUS_UNKNOWN_COMMAND      0x00FF

/* Maximum limits */
#define MAX_TRANSFER_SIZE           0x100000    /* 1MB */
#define DUMP_CHUNK_SIZE             0x100000    /* 1MB chunks */
#define MAX_MEMORY_REGIONS          64

/* Protocol structures */
#pragma pack(push, 1)

typedef struct {
    uint32_t magic;
    uint32_t command;
    uint32_t status;
    uint32_t data_size;
    uint64_t param1;
    uint64_t param2;
    uint64_t param3;
    uint32_t reserved[4];
} DMAPacket;

typedef struct {
    uint64_t start_addr;
    uint64_t size;
    uint32_t flags;
    uint32_t reserved;
} MemoryRegion;

typedef struct {
    uint32_t pid;
    uint32_t ppid;
    uint64_t eprocess_addr;
    uint64_t cr3;
    uint64_t peb_addr;
    char name[16];
    uint32_t threads;
    uint32_t handles;
    uint64_t create_time;
} ProcessInfo;

#pragma pack(pop)

/* Memory region flags */
#define MEMORY_FLAG_SYSTEM          0x01
#define MEMORY_FLAG_RAM             0x02
#define MEMORY_FLAG_RESERVED        0x04
#define MEMORY_FLAG_MMIO            0x08

/* Utility macros */
#define DMA_PACKET_SIZE             sizeof(DMAPacket)
#define MEMORY_REGION_SIZE          sizeof(MemoryRegion)
#define PROCESS_INFO_SIZE           sizeof(ProcessInfo)

/* Validation macros */
#define IS_VALID_MAGIC(magic)       ((magic) == DMA_MAGIC)
#define IS_VALID_COMMAND(cmd)       ((cmd) >= CMD_PING && (cmd) <= CMD_GET_MEMORY_LAYOUT)
#define IS_VALID_SIZE(size)         ((size) > 0 && (size) <= MAX_TRANSFER_SIZE)

#ifdef __cplusplus
}
#endif

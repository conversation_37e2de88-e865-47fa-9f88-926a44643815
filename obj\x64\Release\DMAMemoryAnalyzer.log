﻿  main.cpp
  dma_client.cpp
C:\Users\<USER>\Desktop\windsurf_dma\src\dma_client.cpp(260,34): error C2589: '(': illegal token on right side of '::'
C:\Users\<USER>\Desktop\windsurf_dma\src\dma_client.cpp(260): error C2062: type 'unknown-type' unexpected
C:\Users\<USER>\Desktop\windsurf_dma\src\dma_client.cpp(260,34): error C2059: syntax error: ')'
C:\Users\<USER>\Desktop\windsurf_dma\src\dma_client.cpp(441,54): error C2589: '(': illegal token on right side of '::'
C:\Users\<USER>\Desktop\windsurf_dma\src\dma_client.cpp(441): error C2062: type 'unknown-type' unexpected
C:\Users\<USER>\Desktop\windsurf_dma\src\dma_client.cpp(441): error C2144: syntax error: 'unknown-type' should be preceded by '('
C:\Users\<USER>\Desktop\windsurf_dma\src\dma_client.cpp(441,108): error C2059: syntax error: ')'
  memory_analyzer.cpp
  volatility_interface.cpp
  The contents of <filesystem> are available only with C++17 or later.
C:\Users\<USER>\Desktop\windsurf_dma\src\volatility_interface.cpp(60,10): error C3083: 'filesystem': the symbol to the left of a '::' must be a type
C:\Users\<USER>\Desktop\windsurf_dma\src\volatility_interface.cpp(60,22): error C2039: 'path': is not a member of 'std'
C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\include\algorithm(40): message : see declaration of 'std'
C:\Users\<USER>\Desktop\windsurf_dma\src\volatility_interface.cpp(60,27): error C2065: 'path': undeclared identifier
C:\Users\<USER>\Desktop\windsurf_dma\src\volatility_interface.cpp(60,27): error C2146: syntax error: missing ';' before identifier 'vol_path'
C:\Users\<USER>\Desktop\windsurf_dma\src\volatility_interface.cpp(60,27): error C3861: 'vol_path': identifier not found
C:\Users\<USER>\Desktop\windsurf_dma\src\volatility_interface.cpp(61,15): error C3083: 'filesystem': the symbol to the left of a '::' must be a type
C:\Users\<USER>\Desktop\windsurf_dma\src\volatility_interface.cpp(61,27): error C2039: 'exists': is not a member of 'std'
C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\include\algorithm(40): message : see declaration of 'std'
C:\Users\<USER>\Desktop\windsurf_dma\src\volatility_interface.cpp(61,34): error C2065: 'vol_path': undeclared identifier
C:\Users\<USER>\Desktop\windsurf_dma\src\volatility_interface.cpp(61,8): error C3861: 'exists': identifier not found
C:\Users\<USER>\Desktop\windsurf_dma\src\volatility_interface.cpp(310,10): error C3083: 'filesystem': the symbol to the left of a '::' must be a type
C:\Users\<USER>\Desktop\windsurf_dma\src\volatility_interface.cpp(310,22): error C2039: 'path': is not a member of 'std'
C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\include\algorithm(40): message : see declaration of 'std'
C:\Users\<USER>\Desktop\windsurf_dma\src\volatility_interface.cpp(310,27): error C2146: syntax error: missing ';' before identifier 'vol_path'
C:\Users\<USER>\Desktop\windsurf_dma\src\volatility_interface.cpp(310,27): error C3861: 'vol_path': identifier not found
C:\Users\<USER>\Desktop\windsurf_dma\src\volatility_interface.cpp(311,17): error C3083: 'filesystem': the symbol to the left of a '::' must be a type
C:\Users\<USER>\Desktop\windsurf_dma\src\volatility_interface.cpp(311,29): error C2039: 'exists': is not a member of 'std'
C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Tools\MSVC\14.29.30133\include\algorithm(40): message : see declaration of 'std'
C:\Users\<USER>\Desktop\windsurf_dma\src\volatility_interface.cpp(311,36): error C2065: 'vol_path': undeclared identifier
C:\Users\<USER>\Desktop\windsurf_dma\src\volatility_interface.cpp(311,35): error C3861: 'exists': identifier not found
C:\Users\<USER>\Desktop\windsurf_dma\src\volatility_interface.cpp(312,13): error C2065: 'vol_path': undeclared identifier
C:\Users\<USER>\Desktop\windsurf_dma\src\volatility_interface.cpp(312,48): error C2065: 'vol_path': undeclared identifier

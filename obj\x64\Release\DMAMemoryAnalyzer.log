﻿  main.cpp
  dma_client.cpp
C:\Users\<USER>\Desktop\windsurf_dma\src\dma_client.cpp(115,38): error C4996: 'gethostbyname': Use getaddrinfo() or GetAddrInfoW() instead or define _WINSOCK_DEPRECATED_NO_WARNINGS to disable deprecated API warnings
C:\Users\<USER>\Desktop\windsurf_dma\src\dma_client.cpp(252,34): error C2589: '(': illegal token on right side of '::'
C:\Users\<USER>\Desktop\windsurf_dma\src\dma_client.cpp(252): error C2062: type 'unknown-type' unexpected
C:\Users\<USER>\Desktop\windsurf_dma\src\dma_client.cpp(252,34): error C2059: syntax error: ')'
C:\Users\<USER>\Desktop\windsurf_dma\src\dma_client.cpp(433,54): error C2589: '(': illegal token on right side of '::'
C:\Users\<USER>\Desktop\windsurf_dma\src\dma_client.cpp(433): error C2062: type 'unknown-type' unexpected
C:\Users\<USER>\Desktop\windsurf_dma\src\dma_client.cpp(433): error C2144: syntax error: 'unknown-type' should be preceded by '('
C:\Users\<USER>\Desktop\windsurf_dma\src\dma_client.cpp(433,108): error C2059: syntax error: ')'
  memory_analyzer.cpp
c1xx : fatal error C1083: Cannot open source file: 'src\memory_analyzer.cpp': No such file or directory
  volatility_interface.cpp
c1xx : fatal error C1083: Cannot open source file: 'src\volatility_interface.cpp': No such file or directory

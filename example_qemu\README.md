# TestDMA QEMU Integration

This directory contains the QEMU device implementation for the TestDMA project.

## Updates to Memory Access API

The code has been updated to use QEMU's current memory access API by:

1. Replacing `cpu_memory_rw_debug` with `cpu_physical_memory_rw`
2. Adding a compatibility layer in `cpu_physical_memory.c/h`
3. Updating include paths to match QEMU's directory structure:
   - Replaced `#include "qemu/cpu-common.h"` with the correct path `#include "exec/cpu-common.h"`
   - Replaced `#include "cpu.h"` with the more specific `#include "exec/cpu-all.h"`

## Building and Installing

### Building the Device

1. Copy the files to your QEMU source directory:
   - `advdma_nic.c` to `hw/misc/`
   - `dma_device.c` to `hw/misc/`
   - `cpu_physical_memory.c` and `cpu_physical_memory.h` to `include/exec/`

2. Add the following lines to `hw/misc/meson.build`:
   ```
   softmmu_ss.add(when: 'CONFIG_ADVDMA_NIC', if_true: files('advdma_nic.c'))
   softmmu_ss.add(when: 'CONFIG_DMA_DEVICE', if_true: files('dma_device.c'))
   ```

3. Add the following lines to `default-configs/devices/i386-softmmu.mak`:
   ```
   CONFIG_ADVDMA_NIC=y
   CONFIG_DMA_DEVICE=y
   ```

### Using the Device

To use the device, add the following options to your QEMU command line:

```
-device advdma-nic,id=advdma0
```

## Testing

The device can be tested using the TestDMA client tools included in this project.

## Memory Access API Notes

The `cpu_physical_memory_rw` function provides a compatibility layer for modern QEMU codebases that may have deprecated the original `cpu_physical_memory_read/write` functions. It uses the `address_space_read/write` functions under the hood.

## Troubleshooting

### Include Path Issues

If you encounter compilation errors related to missing include files:

1. **Check for path issues**: QEMU's include structure is hierarchical. Common headers may need to be referenced with their full path.
   
   Common fixes:
   - Replace `#include "cpu.h"` with `#include "exec/cpu-all.h"` 
   - Replace `#include "qemu/cpu-common.h"` with `#include "exec/cpu-common.h"`
   - Use target-specific includes where needed: `#include "target/i386/cpu.h"`

2. **Check QEMU version**: Different QEMU versions may have slightly different include paths. 
   Consult your specific QEMU version's include directory structure.

3. **Update include paths**: If necessary, modify the include paths in the device files to match your QEMU directory structure.

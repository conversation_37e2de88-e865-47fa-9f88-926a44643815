/*
 * Volatility 3 Interface Implementation
 * Wrapper for Volatility 3 memory analysis framework
 */

#include "volatility_interface.h"
#include <iostream>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <filesystem>
#include <cstdio>

#ifdef _WIN32
#include <windows.h>
#include <process.h>
#else
#include <sys/wait.h>
#include <unistd.h>
#endif

VolatilityInterface::VolatilityInterface()
    : m_initialized(false) {
#ifdef _WIN32
    m_python_path = "python";
#else
    m_python_path = "python3";
#endif
}

VolatilityInterface::~VolatilityInterface() {
}

bool VolatilityInterface::Initialize(const std::string& volatility_path) {
    m_volatility_path = volatility_path;
    
    if (!CheckVolatilityPath()) {
        SetError("Volatility path not found or invalid: " + volatility_path);
        return false;
    }

    // Test basic functionality
    std::string version = GetVolatilityVersion();
    if (version.empty()) {
        SetError("Failed to get Volatility version");
        return false;
    }

    m_initialized = true;
    m_last_error.clear();
    return true;
}

bool VolatilityInterface::CheckVolatilityPath() {
    if (m_volatility_path.empty()) {
        return false;
    }

    // Check if file exists
    std::filesystem::path vol_path(m_volatility_path);
    if (!std::filesystem::exists(vol_path)) {
        return false;
    }

    return true;
}

std::string VolatilityInterface::ExecuteCommand(const std::vector<std::string>& command_args, int timeout_seconds) {
    if (!m_initialized) {
        SetError("Not initialized");
        return "";
    }

    // Build command line
    std::string command = m_python_path + " \"" + m_volatility_path + "\"";
    for (const auto& arg : command_args) {
        command += " " + arg;
    }

#ifdef _WIN32
    // Windows implementation
    SECURITY_ATTRIBUTES sa;
    sa.nLength = sizeof(SECURITY_ATTRIBUTES);
    sa.lpSecurityDescriptor = NULL;
    sa.bInheritHandle = TRUE;

    HANDLE hRead, hWrite;
    if (!CreatePipe(&hRead, &hWrite, &sa, 0)) {
        SetError("Failed to create pipe");
        return "";
    }

    STARTUPINFOA si;
    PROCESS_INFORMATION pi;
    ZeroMemory(&si, sizeof(si));
    si.cb = sizeof(si);
    si.hStdOutput = hWrite;
    si.hStdError = hWrite;
    si.dwFlags |= STARTF_USESTDHANDLES;

    ZeroMemory(&pi, sizeof(pi));

    if (!CreateProcessA(NULL, const_cast<char*>(command.c_str()), NULL, NULL, TRUE, 0, NULL, NULL, &si, &pi)) {
        SetError("Failed to execute command");
        CloseHandle(hRead);
        CloseHandle(hWrite);
        return "";
    }

    CloseHandle(hWrite);

    // Read output
    std::string output;
    char buffer[4096];
    DWORD bytesRead;
    
    while (ReadFile(hRead, buffer, sizeof(buffer) - 1, &bytesRead, NULL) && bytesRead > 0) {
        buffer[bytesRead] = '\0';
        output += buffer;
        
        if (output.size() > MAX_OUTPUT_SIZE) {
            break;
        }
    }

    // Wait for process to complete
    DWORD waitResult = WaitForSingleObject(pi.hProcess, timeout_seconds * 1000);
    if (waitResult == WAIT_TIMEOUT) {
        TerminateProcess(pi.hProcess, 1);
        SetError("Command timed out");
    }

    CloseHandle(hRead);
    CloseHandle(pi.hProcess);
    CloseHandle(pi.hThread);

    return output;
#else
    // Linux implementation (simplified)
    FILE* pipe = popen(command.c_str(), "r");
    if (!pipe) {
        SetError("Failed to execute command");
        return "";
    }

    std::string output;
    char buffer[4096];
    while (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        output += buffer;
        if (output.size() > MAX_OUTPUT_SIZE) {
            break;
        }
    }

    pclose(pipe);
    return output;
#endif
}

std::string VolatilityInterface::GetSystemInfo(const std::string& memory_file) {
    std::vector<std::string> args = {"-f", "\"" + memory_file + "\"", VolatilityPlugins::WINDOWS_INFO};
    return ExecuteCommand(args);
}

std::string VolatilityInterface::ListProcesses(const std::string& memory_file) {
    std::vector<std::string> args = {"-f", "\"" + memory_file + "\"", VolatilityPlugins::WINDOWS_PSLIST};
    return ExecuteCommand(args);
}

std::string VolatilityInterface::GetProcessTree(const std::string& memory_file) {
    std::vector<std::string> args = {"-f", "\"" + memory_file + "\"", VolatilityPlugins::WINDOWS_PSTREE};
    return ExecuteCommand(args);
}

std::string VolatilityInterface::ListModules(const std::string& memory_file, uint32_t pid) {
    std::vector<std::string> args = {"-f", "\"" + memory_file + "\"", VolatilityPlugins::WINDOWS_DLLLIST};
    if (pid != 0) {
        args.push_back("--pid");
        args.push_back(std::to_string(pid));
    }
    return ExecuteCommand(args);
}

std::string VolatilityInterface::DumpProcessMemory(const std::string& memory_file, uint32_t pid, const std::string& output_dir) {
    std::vector<std::string> args = {
        "-f", "\"" + memory_file + "\"",
        VolatilityPlugins::WINDOWS_MEMMAP,
        "--pid", std::to_string(pid),
        "--dump",
        "--output-dir", "\"" + output_dir + "\""
    };
    return ExecuteCommand(args);
}

std::string VolatilityInterface::ScanMemory(const std::string& memory_file, const std::string& pattern) {
    std::vector<std::string> args = {
        "-f", "\"" + memory_file + "\"",
        VolatilityPlugins::WINDOWS_MALFIND
    };
    return ExecuteCommand(args);
}

std::string VolatilityInterface::GetNetworkConnections(const std::string& memory_file) {
    std::vector<std::string> args = {"-f", "\"" + memory_file + "\"", VolatilityPlugins::WINDOWS_NETSCAN};
    return ExecuteCommand(args);
}

std::string VolatilityInterface::GetFileHandles(const std::string& memory_file, uint32_t pid) {
    std::vector<std::string> args = {"-f", "\"" + memory_file + "\"", VolatilityPlugins::WINDOWS_HANDLES};
    if (pid != 0) {
        args.push_back("--pid");
        args.push_back(std::to_string(pid));
    }
    return ExecuteCommand(args);
}

std::string VolatilityInterface::GetPasswordHashes(const std::string& memory_file) {
    std::vector<std::string> args = {"-f", "\"" + memory_file + "\"", VolatilityPlugins::WINDOWS_HASHDUMP};
    return ExecuteCommand(args);
}

std::string VolatilityInterface::ExecuteCustomCommand(const std::string& memory_file, const std::string& plugin, 
                                                    const std::vector<std::string>& args) {
    std::vector<std::string> command_args = {"-f", "\"" + memory_file + "\"", plugin};
    command_args.insert(command_args.end(), args.begin(), args.end());
    return ExecuteCommand(command_args);
}

std::vector<std::string> VolatilityInterface::GetAvailablePlugins() {
    std::vector<std::string> plugins;
    
    std::vector<std::string> args = {"--help"};
    std::string help_output = ExecuteCommand(args);
    
    // Parse help output to extract plugin names
    std::istringstream iss(help_output);
    std::string line;
    bool in_plugins_section = false;
    
    while (std::getline(iss, line)) {
        if (line.find("available plugins") != std::string::npos) {
            in_plugins_section = true;
            continue;
        }
        
        if (in_plugins_section && !line.empty() && line[0] != ' ') {
            break; // End of plugins section
        }
        
        if (in_plugins_section && line.find("windows.") != std::string::npos) {
            size_t start = line.find("windows.");
            if (start != std::string::npos) {
                size_t end = line.find(" ", start);
                if (end == std::string::npos) end = line.length();
                plugins.push_back(line.substr(start, end - start));
            }
        }
    }
    
    return plugins;
}

bool VolatilityInterface::ValidateMemoryFile(const std::string& memory_file) {
    if (memory_file.empty()) {
        return false;
    }

    // Check if file exists
    std::ifstream file(memory_file, std::ios::binary);
    if (!file.is_open()) {
        return false;
    }

    // Check file size (should be at least a few MB for a valid memory dump)
    file.seekg(0, std::ios::end);
    std::streamsize size = file.tellg();
    file.close();

    return size > 1024 * 1024; // At least 1MB
}

std::string VolatilityInterface::GetVolatilityVersion() {
    std::vector<std::string> args = {"--version"};
    std::string output = ExecuteCommand(args, 10); // 10 second timeout
    
    // Extract version from output
    if (!output.empty()) {
        std::istringstream iss(output);
        std::string line;
        while (std::getline(iss, line)) {
            if (line.find("Volatility") != std::string::npos) {
                return line;
            }
        }
    }
    
    return output;
}

void VolatilityInterface::SetError(const std::string& error) {
    m_last_error = error;
}

// Utility functions
bool IsValidVolatilityPath(const std::string& path) {
    if (path.empty()) {
        return false;
    }
    
    std::filesystem::path vol_path(path);
    return std::filesystem::exists(vol_path) && 
           (vol_path.filename() == "vol.py" || vol_path.filename() == "volatility");
}

std::string FindVolatilityPath() {
    std::vector<std::string> possible_paths = {
        "volatility3/vol.py",
        "vol.py",
        "volatility/vol.py",
        "/usr/local/bin/vol.py",
        "/opt/volatility3/vol.py"
    };
    
    for (const auto& path : possible_paths) {
        if (IsValidVolatilityPath(path)) {
            return path;
        }
    }
    
    return "";
}

std::vector<std::string> SplitString(const std::string& str, char delimiter) {
    std::vector<std::string> tokens;
    std::istringstream iss(str);
    std::string token;
    
    while (std::getline(iss, token, delimiter)) {
        tokens.push_back(token);
    }
    
    return tokens;
}

#!/usr/bin/env python3
"""
Deploy Streamlined DMA Device to QEMU
Copies the device file and updates QEMU build configuration
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def run_command(cmd, cwd=None):
    """Run a command and return success status"""
    try:
        print(f"Running: {cmd}")
        result = subprocess.run(cmd, shell=True, cwd=cwd, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"Command failed: {cmd}")
            print(f"Error: {result.stderr}")
            return False, result.stderr
        print(f"Success: {result.stdout}")
        return True, result.stdout
    except Exception as e:
        print(f"Failed to run command: {cmd}")
        print(f"Error: {e}")
        return False, str(e)

def copy_device_file(qemu_source_path):
    """Copy streamlined DMA device to QEMU source"""
    source_file = Path("example_qemu/hw/misc/streamlined_dma.c")
    if not source_file.exists():
        print(f"Source file not found: {source_file}")
        return False
    
    # Target path in QEMU source
    target_dir = Path(qemu_source_path) / "hw" / "misc"
    target_file = target_dir / "streamlined_dma.c"
    
    if not target_dir.exists():
        print(f"QEMU source directory not found: {target_dir}")
        return False
    
    try:
        shutil.copy2(source_file, target_file)
        print(f"Copied {source_file} to {target_file}")
        return True
    except Exception as e:
        print(f"Failed to copy file: {e}")
        return False

def update_meson_build(qemu_source_path):
    """Update meson.build to include the new device"""
    meson_file = Path(qemu_source_path) / "hw" / "misc" / "meson.build"
    
    if not meson_file.exists():
        print(f"meson.build not found: {meson_file}")
        return False
    
    # Read current meson.build
    try:
        with open(meson_file, 'r') as f:
            content = f.read()
        
        # Check if our device is already added
        if 'streamlined_dma.c' in content:
            print("streamlined_dma.c already in meson.build")
            return True
        
        # Add our device to the misc_ss.add() section
        lines = content.split('\n')
        new_lines = []
        added = False
        
        for line in lines:
            new_lines.append(line)
            # Look for misc_ss.add() calls and add our device
            if 'misc_ss.add(' in line and '.c' in line and not added:
                # Add our device after this line
                indent = len(line) - len(line.lstrip())
                new_lines.append(' ' * indent + "misc_ss.add(files('streamlined_dma.c'))")
                added = True
        
        if not added:
            # If we couldn't find a good place, add at the end
            new_lines.append("misc_ss.add(files('streamlined_dma.c'))")
        
        # Write back to file
        with open(meson_file, 'w') as f:
            f.write('\n'.join(new_lines))
        
        print("Updated meson.build to include streamlined_dma.c")
        return True
        
    except Exception as e:
        print(f"Failed to update meson.build: {e}")
        return False

def create_vm_config_snippet():
    """Create a VM configuration snippet for the DMA device"""
    config_snippet = """
# Add this to your VM configuration to enable the Streamlined DMA device
# In Proxmox, edit /etc/pve/qemu-server/<vmid>.conf and add:

args: -device streamlined-dma,id=dma0

# Or add via command line when starting QEMU:
# -device streamlined-dma,id=dma0

# The device will listen on port 31338 by default
# Connect from external tools using: <proxmox_ip>:31338
"""
    
    with open("vm_config_snippet.txt", "w") as f:
        f.write(config_snippet)
    
    print("Created vm_config_snippet.txt with configuration instructions")

def create_test_script():
    """Create a test script for the deployment"""
    test_script = """#!/bin/bash
# Test script for Streamlined DMA device deployment

echo "Testing Streamlined DMA device deployment..."

# Check if the device file exists in QEMU source
if [ -f "hw/misc/streamlined_dma.c" ]; then
    echo "✓ streamlined_dma.c found in QEMU source"
else
    echo "✗ streamlined_dma.c not found in QEMU source"
    exit 1
fi

# Check if meson.build includes our device
if grep -q "streamlined_dma.c" hw/misc/meson.build; then
    echo "✓ streamlined_dma.c found in meson.build"
else
    echo "✗ streamlined_dma.c not found in meson.build"
    exit 1
fi

# Build QEMU
echo "Building QEMU..."
cd build
if make -j$(nproc); then
    echo "✓ QEMU build successful"
else
    echo "✗ QEMU build failed"
    exit 1
fi

# Install QEMU
echo "Installing QEMU..."
if make install; then
    echo "✓ QEMU installation successful"
else
    echo "✗ QEMU installation failed"
    exit 1
fi

echo "Deployment test completed successfully!"
echo "You can now start your VM and the Streamlined DMA device should be available."
"""
    
    with open("test_deployment.sh", "w") as f:
        f.write(test_script)
    
    os.chmod("test_deployment.sh", 0o755)
    print("Created test_deployment.sh script")

def main():
    print("Streamlined DMA Device Deployment Script")
    print("=" * 50)
    
    # Get QEMU source path
    if len(sys.argv) > 1:
        qemu_source_path = sys.argv[1]
    else:
        qemu_source_path = input("Enter QEMU source path (e.g., /path/to/qemu): ").strip()
    
    if not qemu_source_path:
        print("QEMU source path is required")
        sys.exit(1)
    
    qemu_path = Path(qemu_source_path)
    if not qemu_path.exists():
        print(f"QEMU source path does not exist: {qemu_path}")
        sys.exit(1)
    
    print(f"Using QEMU source path: {qemu_path}")
    
    # Copy device file
    if not copy_device_file(qemu_source_path):
        print("Failed to copy device file")
        sys.exit(1)
    
    # Update meson.build
    if not update_meson_build(qemu_source_path):
        print("Failed to update meson.build")
        sys.exit(1)
    
    # Create configuration files
    create_vm_config_snippet()
    create_test_script()
    
    print("\nDeployment preparation completed!")
    print("\nNext steps:")
    print("1. Copy test_deployment.sh to your QEMU source directory")
    print("2. Run: chmod +x test_deployment.sh && ./test_deployment.sh")
    print("3. Add the device to your VM configuration using vm_config_snippet.txt")
    print("4. Start your VM and test the DMA device")
    
    print("\nManual deployment steps:")
    print(f"1. scp example_qemu/hw/misc/streamlined_dma.c user@proxmox:{qemu_source_path}/hw/misc/")
    print(f"2. ssh user@proxmox")
    print(f"3. cd {qemu_source_path}")
    print("4. Edit hw/misc/meson.build to add: misc_ss.add(files('streamlined_dma.c'))")
    print("5. cd build && make -j$(nproc) && make install")
    print("6. Add -device streamlined-dma,id=dma0 to VM configuration")

if __name__ == "__main__":
    main()

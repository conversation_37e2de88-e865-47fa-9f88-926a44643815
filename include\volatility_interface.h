#pragma once
/*
 * Volatility 3 Interface
 * Wrapper for Volatility 3 memory analysis framework
 */

#include <string>
#include <vector>
#include <map>

class VolatilityInterface {
public:
    VolatilityInterface();
    ~VolatilityInterface();

    /* Initialization */
    bool Initialize(const std::string& volatility_path);
    bool IsInitialized() const { return m_initialized; }

    /* Core analysis functions */
    std::string GetSystemInfo(const std::string& memory_file);
    std::string ListProcesses(const std::string& memory_file);
    std::string GetProcessTree(const std::string& memory_file);
    std::string ListModules(const std::string& memory_file, uint32_t pid = 0);
    std::string DumpProcessMemory(const std::string& memory_file, uint32_t pid, const std::string& output_dir);

    /* Advanced analysis */
    std::string ScanMemory(const std::string& memory_file, const std::string& pattern);
    std::string GetNetworkConnections(const std::string& memory_file);
    std::string GetRegistryHives(const std::string& memory_file);
    std::string GetFileHandles(const std::string& memory_file, uint32_t pid = 0);

    /* Hash and forensics */
    std::string GetPasswordHashes(const std::string& memory_file);
    std::string GetCachedFiles(const std::string& memory_file);
    std::string GetMalwareAnalysis(const std::string& memory_file);

    /* Custom commands */
    std::string ExecuteCustomCommand(const std::string& memory_file, const std::string& plugin, 
                                   const std::vector<std::string>& args = {});

    /* Utility functions */
    std::vector<std::string> GetAvailablePlugins();
    bool ValidateMemoryFile(const std::string& memory_file);
    std::string GetVolatilityVersion();

    /* Error handling */
    std::string GetLastError() const { return m_last_error; }
    bool HasError() const { return !m_last_error.empty(); }

private:
    /* Internal methods */
    std::string ExecuteCommand(const std::vector<std::string>& command_args, int timeout_seconds = 300);
    bool CheckVolatilityPath();
    void SetError(const std::string& error);
    std::vector<std::string> ParseCommandOutput(const std::string& output);

    /* Member variables */
    std::string m_volatility_path;
    std::string m_python_path;
    bool m_initialized;
    std::string m_last_error;

    /* Configuration */
    static const int DEFAULT_TIMEOUT = 300; // 5 minutes
    static const int MAX_OUTPUT_SIZE = 100 * 1024 * 1024; // 100MB
};

/* Volatility plugin constants */
namespace VolatilityPlugins {
    const std::string WINDOWS_INFO = "windows.info";
    const std::string WINDOWS_PSLIST = "windows.pslist";
    const std::string WINDOWS_PSTREE = "windows.pstree";
    const std::string WINDOWS_DLLLIST = "windows.dlllist";
    const std::string WINDOWS_MEMMAP = "windows.memmap";
    const std::string WINDOWS_NETSCAN = "windows.netscan";
    const std::string WINDOWS_FILESCAN = "windows.filescan";
    const std::string WINDOWS_HASHDUMP = "windows.hashdump";
    const std::string WINDOWS_MALFIND = "windows.malfind";
    const std::string WINDOWS_HANDLES = "windows.handles";
    const std::string WINDOWS_CMDLINE = "windows.cmdline";
}

/* Utility functions */
bool IsValidVolatilityPath(const std::string& path);
std::string FindVolatilityPath();
std::vector<std::string> SplitString(const std::string& str, char delimiter);
